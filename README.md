# PKI Document Signing System

A comprehensive Public Key Infrastructure (PKI) system for secure document signing, encryption, and verification with advanced security features and attack simulation capabilities.

## 🚀 Features

### Core PKI Functionality
- **Certificate Authority (CA)** - Self-signed CA for issuing and managing digital certificates
- **User Registration** - Generate RSA key pairs and X.509 certificates for users
- **Document Signing** - Sign documents using RSA-PSS with SHA-256
- **Signature Verification** - Verify document signatures with certificate validation
- **Certificate Validation** - Comprehensive certificate verification including CA signature validation

### Advanced Security Features
- **Hybrid Encryption** - RSA + AES encryption for document confidentiality
- **Attack Simulation** - Test system security against various attack vectors
- **Error Handling** - Comprehensive error management with detailed logging
- **Security Logging** - Detailed audit trail of all operations

### User Interface
- **Command Line Interface (CLI)** - Interactive menu-driven interface
- **Comprehensive Testing** - Unit tests, integration tests, and performance tests
- **System Reporting** - Detailed system status and configuration reports

## 📁 Project Structure

```
PKI_document_signing/
├── ca.py                   # Certificate Authority operations
├── user.py                 # User registration and management
├── signer.py               # Document signing functionality
├── verifier.py             # Signature verification with enhanced validation
├── encryptor.py            # Hybrid encryption (RSA + AES)
├── attack_simulator.py     # Security testing and attack simulation
├── utils.py                # Utility functions and system management
├── error_handler.py        # Comprehensive error handling
├── cli.py                  # Command line interface
├── test_app.py             # Basic functionality tests
├── test_suite.py           # Comprehensive test suite
├── performance_test.py     # Performance benchmarking
└── README.md               # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Dependencies
```bash
pip install cryptography
```

### Quick Start
```bash
# Clone or download the project
cd PKI_document_signing

# Install dependencies
pip install cryptography

# Run the CLI interface
python cli.py

# Or run basic tests
python test_app.py

# Or run comprehensive tests
python test_suite.py
```

## 📖 Usage

### Command Line Interface
The easiest way to use the system is through the interactive CLI:

```bash
python cli.py
```

This provides a menu-driven interface for all operations:
- 🏗️ Setup & Management
- 👤 User Operations  
- 📝 Document Operations
- 🔐 Encryption Operations
- 🔍 Verification & Info
- 🛡️ Security Testing
- 📊 System Reports

### Programmatic Usage

#### Basic PKI Operations
```python
from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature

# Initialize PKI
create_ca()
register_user("alice")

# Sign and verify a document
sign_document("alice", "contract.txt")
verify_signature("alice", "contract.txt")
```

#### Hybrid Encryption
```python
from encryptor import encrypt_document, decrypt_document

# Encrypt document for a user
encrypt_document("alice", "secret.txt")

# Decrypt document
decrypt_document("alice", "secret.txt.enc")
```

#### Security Testing
```python
from attack_simulator import AttackSimulator

# Run security tests
simulator = AttackSimulator()
simulator.run_all_attacks("signed_doc.txt", "encrypted_file.enc")
```

## 🔒 Security Features

### Cryptographic Standards
- **RSA 2048-bit** keys for asymmetric operations
- **AES-256-CBC** for symmetric encryption
- **RSA-PSS** with SHA-256 for digital signatures
- **OAEP padding** for RSA encryption
- **X.509 v3** certificates with CA validation

### Attack Resistance
The system is tested against:
- **Document Tampering** - Modification of signed documents
- **Signature Spoofing** - Fake signature creation attempts
- **Certificate Spoofing** - Fraudulent certificate attacks
- **Encryption Attacks** - Unauthorized decryption attempts

### Security Validation
- Certificate expiration checking
- CA signature verification
- Comprehensive input validation
- Secure key generation and storage

## 🧪 Testing

### Run All Tests
```bash
# Comprehensive test suite (24 tests)
python test_suite.py

# Basic functionality tests
python test_app.py

# Performance benchmarking
python performance_test.py
```

### Test Coverage
- **Unit Tests** - Individual component testing
- **Integration Tests** - End-to-end workflow testing
- **Security Tests** - Attack simulation and resistance
- **Performance Tests** - Operation timing and scalability
- **Error Handling Tests** - Exception and validation testing

## 📊 System Reports

Generate comprehensive system reports:

```python
from utils import generate_system_report, save_report_to_file

report = generate_system_report()
save_report_to_file(report, "system_status.txt")
```

Reports include:
- PKI setup status
- User registrations
- File inventory
- Certificate details
- System configuration

## 🔧 Configuration

### Certificate Validity
- **CA Certificate**: 10 years
- **User Certificates**: 1 year
- **Country Code**: NP (Nepal)
- **Organization**: SecurePKI Inc.

### Key Specifications
- **Algorithm**: RSA
- **Key Size**: 2048 bits
- **Public Exponent**: 65537
- **Signature Algorithm**: SHA-256 with RSA-PSS

## 🚨 Error Handling

The system includes comprehensive error handling:

- **Custom Exception Classes** - Specific error types for different operations
- **Detailed Error Messages** - Clear descriptions with error codes
- **Logging Integration** - All errors logged with context
- **Graceful Degradation** - System continues operation when possible

### Error Types
- `PKIError` - Base PKI system error
- `CertificateError` - Certificate-related issues
- `SignatureError` - Signature operation failures
- `EncryptionError` - Encryption/decryption problems
- `ValidationError` - Input validation failures

## 📈 Performance

Typical operation times (on modern hardware):
- **CA Creation**: ~0.03s
- **User Registration**: ~0.10s
- **Document Signing**: ~0.05s (independent of file size)
- **Signature Verification**: ~0.05s
- **Encryption**: ~0.02s per KB
- **Decryption**: ~0.02s per KB

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is for educational and demonstration purposes. Please ensure compliance with local cryptographic regulations.

## 🔍 Security Notice

This implementation is designed for educational purposes and demonstration of PKI concepts. For production use, consider:
- Hardware Security Modules (HSMs) for key storage
- Certificate Revocation Lists (CRLs)
- Online Certificate Status Protocol (OCSP)
- Key escrow and recovery mechanisms
- Compliance with industry standards (FIPS 140-2, Common Criteria)

## 📞 Support

For questions or issues:
1. Check the comprehensive test suite for usage examples
2. Review the CLI help system (`python cli.py` → Help)
3. Examine the generated system reports for configuration details

---

**Built with Python 3.12 and the `cryptography` library**
