"""
Attack Simulation Module for PKI Document Signing System
Implements various attack scenarios to test system security
"""

import os
import shutil
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameO<PERSON>
from datetime import datetime, timedelta


class AttackSimulator:
    """Class to simulate various attacks on the PKI system"""
    
    def __init__(self):
        self.attack_results = []
    
    def log_attack_result(self, attack_name, success, details):
        """Log the result of an attack"""
        result = {
            'attack': attack_name,
            'success': success,
            'details': details
        }
        self.attack_results.append(result)
        status = "SUCCESS" if success else "FAILED"
        print(f"[ATTACK] {attack_name}: {status} - {details}")
    
    def document_tampering_attack(self, doc_path):
        """
        Simulate document tampering attack
        Modify a signed document and try to verify it
        """
        print("\n--- Document Tampering Attack ---")
        
        try:
            # Create backup of original document
            backup_path = f"{doc_path}.backup"
            shutil.copy2(doc_path, backup_path)
            
            # Tamper with the document
            with open(doc_path, "r") as f:
                original_content = f.read()
            
            tampered_content = original_content.replace("Alice", "Mallory")
            
            with open(doc_path, "w") as f:
                f.write(tampered_content)
            
            print(f"[INFO] Document tampered: '{doc_path}'")
            print(f"[INFO] Original: {original_content[:50]}...")
            print(f"[INFO] Tampered: {tampered_content[:50]}...")
            
            # Try to verify the tampered document
            from verifier import verify_signature

            # This should fail
            try:
                verify_signature("alice", doc_path)
                self.log_attack_result("Document Tampering", True, "Tampered document verified (SECURITY BREACH!)")
            except Exception:
                self.log_attack_result("Document Tampering", False, "Tampered document correctly rejected")
            
            # Restore original document
            shutil.copy2(backup_path, doc_path)
            os.remove(backup_path)
            
        except Exception as e:
            self.log_attack_result("Document Tampering", False, f"Attack failed with error: {e}")
    
    def signature_spoofing_attack(self, username, doc_path):
        """
        Simulate signature spoofing attack
        Try to create a fake signature for a document
        """
        print("\n--- Signature Spoofing Attack ---")
        
        try:
            # Generate a fake key pair
            fake_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
            
            # Read the document
            with open(doc_path, "rb") as f:
                data = f.read()
            
            # Create a fake signature with the fake key
            fake_signature = fake_key.sign(
                data,
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
            
            # Save the fake signature
            fake_sig_path = f"{doc_path}.fake_sig"
            with open(fake_sig_path, "wb") as f:
                f.write(fake_signature)
            
            # Backup original signature and replace with fake
            original_sig_path = f"{doc_path}.sig"
            backup_sig_path = f"{doc_path}.sig.backup"
            
            if os.path.exists(original_sig_path):
                shutil.copy2(original_sig_path, backup_sig_path)
            
            shutil.copy2(fake_sig_path, original_sig_path)
            
            # Try to verify with the fake signature
            from verifier import verify_signature
            
            try:
                verify_signature(username, doc_path)
                self.log_attack_result("Signature Spoofing", True, "Fake signature verified (SECURITY BREACH!)")
            except Exception:
                self.log_attack_result("Signature Spoofing", False, "Fake signature correctly rejected")
            
            # Restore original signature
            if os.path.exists(backup_sig_path):
                shutil.copy2(backup_sig_path, original_sig_path)
                os.remove(backup_sig_path)
            
            # Clean up
            os.remove(fake_sig_path)
            
        except Exception as e:
            self.log_attack_result("Signature Spoofing", False, f"Attack failed with error: {e}")
    
    def certificate_spoofing_attack(self, target_username):
        """
        Simulate certificate spoofing attack
        Try to create a fake certificate for a user
        """
        print("\n--- Certificate Spoofing Attack ---")
        
        try:
            # Generate fake key pair
            fake_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
            
            # Create a self-signed fake certificate (without CA signature)
            subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "NP"),
                x509.NameAttribute(NameOID.COMMON_NAME, target_username)
            ])
            
            fake_cert = x509.CertificateBuilder().subject_name(subject).issuer_name(
                subject  # Self-signed
            ).public_key(fake_key.public_key()).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=365)
            ).sign(fake_key, hashes.SHA256())  # Self-signed with fake key
            
            # Backup original certificate
            original_cert_path = f"{target_username}_cert.pem"
            backup_cert_path = f"{target_username}_cert.pem.backup"
            
            if os.path.exists(original_cert_path):
                shutil.copy2(original_cert_path, backup_cert_path)
            
            # Replace with fake certificate
            with open(original_cert_path, "wb") as f:
                f.write(fake_cert.public_bytes(serialization.Encoding.PEM))
            
            # Try to verify a signature using the fake certificate
            # First, create a document and sign it with the fake key
            test_doc = "fake_test_doc.txt"
            with open(test_doc, "w") as f:
                f.write("This is a test document for certificate spoofing attack.")
            
            # Sign with fake key
            with open(test_doc, "rb") as f:
                data = f.read()
            
            fake_signature = fake_key.sign(
                data,
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
            
            with open(f"{test_doc}.sig", "wb") as f:
                f.write(fake_signature)
            
            # Try to verify
            from verifier import verify_signature
            
            try:
                verify_signature(target_username, test_doc)
                self.log_attack_result("Certificate Spoofing", True, "Fake certificate accepted (SECURITY BREACH!)")
            except Exception:
                self.log_attack_result("Certificate Spoofing", False, "Fake certificate correctly rejected")
            
            # Restore original certificate
            if os.path.exists(backup_cert_path):
                shutil.copy2(backup_cert_path, original_cert_path)
                os.remove(backup_cert_path)
            
            # Clean up
            os.remove(test_doc)
            os.remove(f"{test_doc}.sig")
            
        except Exception as e:
            self.log_attack_result("Certificate Spoofing", False, f"Attack failed with error: {e}")
    
    def encryption_attack(self, encrypted_file_path):
        """
        Simulate encryption attack
        Try to decrypt a file without the proper private key
        """
        print("\n--- Encryption Attack ---")
        
        try:
            # Generate attacker's key pair
            attacker_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
            
            # Try to decrypt with wrong key
            from encryptor import decrypt_document
            
            # Save attacker's key temporarily
            attacker_key_path = "attacker_key.pem"
            with open(attacker_key_path, "wb") as f:
                f.write(attacker_key.private_bytes(
                    serialization.Encoding.PEM,
                    serialization.PrivateFormat.TraditionalOpenSSL,
                    serialization.NoEncryption()
                ))
            
            # Backup original key and replace with attacker's key
            original_key_path = "bob_key.pem"  # Assuming we're attacking Bob's encrypted file
            backup_key_path = "bob_key.pem.backup"
            
            if os.path.exists(original_key_path):
                shutil.copy2(original_key_path, backup_key_path)
                shutil.copy2(attacker_key_path, original_key_path)
            
            # Try to decrypt
            result = decrypt_document("bob", encrypted_file_path)
            
            if result:
                self.log_attack_result("Encryption Attack", True, "Encrypted file decrypted with wrong key (SECURITY BREACH!)")
            else:
                self.log_attack_result("Encryption Attack", False, "Encrypted file correctly protected")
            
            # Restore original key
            if os.path.exists(backup_key_path):
                shutil.copy2(backup_key_path, original_key_path)
                os.remove(backup_key_path)
            
            # Clean up
            os.remove(attacker_key_path)
            
        except Exception as e:
            self.log_attack_result("Encryption Attack", False, f"Attack failed with error: {e}")
    
    def run_all_attacks(self, signed_doc_path, encrypted_file_path):
        """Run all attack simulations"""
        print("\n" + "="*50)
        print("RUNNING SECURITY ATTACK SIMULATIONS")
        print("="*50)
        
        self.document_tampering_attack(signed_doc_path)
        self.signature_spoofing_attack("alice", signed_doc_path)
        self.certificate_spoofing_attack("alice")
        self.encryption_attack(encrypted_file_path)
        
        self.print_attack_summary()
    
    def print_attack_summary(self):
        """Print summary of all attacks"""
        print("\n" + "="*50)
        print("ATTACK SIMULATION SUMMARY")
        print("="*50)
        
        successful_attacks = 0
        total_attacks = len(self.attack_results)
        
        for result in self.attack_results:
            status = "🔴 BREACH" if result['success'] else "🟢 SECURE"
            print(f"{status} {result['attack']}: {result['details']}")
            if result['success']:
                successful_attacks += 1
        
        print(f"\nSecurity Score: {total_attacks - successful_attacks}/{total_attacks} attacks prevented")
        
        if successful_attacks == 0:
            print("🎉 EXCELLENT! All attacks were successfully prevented.")
        else:
            print(f"⚠️  WARNING! {successful_attacks} attack(s) succeeded. Review security measures.")
