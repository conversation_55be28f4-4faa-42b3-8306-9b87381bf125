#!/usr/bin/env python3
"""
Command Line Interface for PKI Document Signing System
Provides an interactive menu-driven interface for all PKI operations
"""

import os
import sys
import argparse
from pathlib import Path

# Import PKI modules
from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature
from encryptor import (
    encrypt_document, decrypt_document, 
    encrypt_and_sign_document, verify_and_decrypt_document
)
from attack_simulator import AttackSimulator
from utils import (
    get_certificate_info, get_key_info, verify_pki_setup,
    generate_system_report, save_report_to_file, list_pki_files
)
from error_handler import PKIError


class PKICLIInterface:
    """Command Line Interface for PKI operations"""
    
    def __init__(self):
        self.running = True
    
    def display_banner(self):
        """Display the application banner"""
        print("\n" + "="*60)
        print("    PKI DOCUMENT SIGNING SYSTEM")
        print("    Secure Document Management with Digital Signatures")
        print("="*60)
    
    def display_main_menu(self):
        """Display the main menu options"""
        print("\n📋 MAIN MENU:")
        print("1.  🏗️  Setup & Management")
        print("2.  👤 User Operations")
        print("3.  📝 Document Operations")
        print("4.  🔐 Encryption Operations")
        print("5.  🔍 Verification & Info")
        print("6.  🛡️  Security Testing")
        print("7.  📊 System Reports")
        print("8.  ❓ Help")
        print("9.  🚪 Exit")
        print("-" * 40)
    
    def display_setup_menu(self):
        """Display setup and management menu"""
        print("\n🏗️  SETUP & MANAGEMENT:")
        print("1. Initialize CA (Certificate Authority)")
        print("2. Check PKI Setup Status")
        print("3. List All PKI Files")
        print("4. Back to Main Menu")
    
    def display_user_menu(self):
        """Display user operations menu"""
        print("\n👤 USER OPERATIONS:")
        print("1. Register New User")
        print("2. View User Certificate Info")
        print("3. View User Key Info")
        print("4. Back to Main Menu")
    
    def display_document_menu(self):
        """Display document operations menu"""
        print("\n📝 DOCUMENT OPERATIONS:")
        print("1. Sign Document")
        print("2. Verify Document Signature")
        print("3. Back to Main Menu")
    
    def display_encryption_menu(self):
        """Display encryption operations menu"""
        print("\n🔐 ENCRYPTION OPERATIONS:")
        print("1. Encrypt Document")
        print("2. Decrypt Document")
        print("3. Encrypt and Sign Document")
        print("4. Verify and Decrypt Document")
        print("5. Back to Main Menu")
    
    def display_verification_menu(self):
        """Display verification and info menu"""
        print("\n🔍 VERIFICATION & INFO:")
        print("1. Verify Certificate")
        print("2. Get Certificate Details")
        print("3. Get Key Details")
        print("4. Verify PKI Setup")
        print("5. Back to Main Menu")
    
    def display_security_menu(self):
        """Display security testing menu"""
        print("\n🛡️  SECURITY TESTING:")
        print("1. Run All Attack Simulations")
        print("2. Document Tampering Test")
        print("3. Signature Spoofing Test")
        print("4. Certificate Spoofing Test")
        print("5. Encryption Attack Test")
        print("6. Back to Main Menu")
    
    def display_reports_menu(self):
        """Display system reports menu"""
        print("\n📊 SYSTEM REPORTS:")
        print("1. Generate Full System Report")
        print("2. View PKI Setup Status")
        print("3. List All Files")
        print("4. Back to Main Menu")
    
    def get_user_input(self, prompt, input_type=str, required=True):
        """Get and validate user input"""
        while True:
            try:
                value = input(f"{prompt}: ").strip()
                if not value and required:
                    print("❌ This field is required. Please enter a value.")
                    continue
                if not value and not required:
                    return None
                return input_type(value)
            except ValueError:
                print(f"❌ Invalid input. Please enter a valid {input_type.__name__}.")
            except KeyboardInterrupt:
                print("\n\n👋 Operation cancelled by user.")
                return None
    
    def get_file_path(self, prompt, must_exist=True):
        """Get and validate file path"""
        while True:
            path = self.get_user_input(prompt)
            if path is None:
                return None
            
            if must_exist and not os.path.exists(path):
                print(f"❌ File not found: {path}")
                continue
            
            return path
    
    def handle_pki_operation(self, operation, *args, **kwargs):
        """Handle PKI operations with error handling"""
        try:
            result = operation(*args, **kwargs)
            return result
        except PKIError as e:
            print(f"❌ PKI Error: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected Error: {e}")
            return None
    
    def setup_operations(self):
        """Handle setup and management operations"""
        while True:
            self.display_setup_menu()
            choice = self.get_user_input("Select option (1-4)", int)
            
            if choice == 1:
                print("\n🏗️  Initializing Certificate Authority...")
                if self.handle_pki_operation(create_ca):
                    print("✅ CA initialized successfully!")
            
            elif choice == 2:
                print("\n🔍 Checking PKI Setup Status...")
                status = self.handle_pki_operation(verify_pki_setup)
                if status:
                    print("✅ PKI setup check completed!")
            
            elif choice == 3:
                print("\n📁 Listing PKI Files...")
                files = self.handle_pki_operation(list_pki_files)
                if files:
                    print("✅ File listing completed!")
            
            elif choice == 4:
                break
            
            else:
                print("❌ Invalid choice. Please select 1-4.")
    
    def user_operations(self):
        """Handle user operations"""
        while True:
            self.display_user_menu()
            choice = self.get_user_input("Select option (1-4)", int)
            
            if choice == 1:
                username = self.get_user_input("Enter username")
                if username:
                    print(f"\n👤 Registering user: {username}")
                    if self.handle_pki_operation(register_user, username):
                        print("✅ User registered successfully!")
            
            elif choice == 2:
                username = self.get_user_input("Enter username")
                if username:
                    cert_path = f"{username}_cert.pem"
                    print(f"\n📜 Getting certificate info for: {username}")
                    info = self.handle_pki_operation(get_certificate_info, cert_path)
                    if info:
                        print("✅ Certificate info retrieved!")
            
            elif choice == 3:
                username = self.get_user_input("Enter username")
                if username:
                    key_path = f"{username}_key.pem"
                    print(f"\n🔑 Getting key info for: {username}")
                    info = self.handle_pki_operation(get_key_info, key_path)
                    if info:
                        print("✅ Key info retrieved!")
            
            elif choice == 4:
                break
            
            else:
                print("❌ Invalid choice. Please select 1-4.")
    
    def document_operations(self):
        """Handle document operations"""
        while True:
            self.display_document_menu()
            choice = self.get_user_input("Select option (1-3)", int)
            
            if choice == 1:
                username = self.get_user_input("Enter signer username")
                doc_path = self.get_file_path("Enter document path")
                if username and doc_path:
                    print(f"\n✍️  Signing document: {doc_path}")
                    if self.handle_pki_operation(sign_document, username, doc_path):
                        print("✅ Document signed successfully!")
            
            elif choice == 2:
                username = self.get_user_input("Enter signer username")
                doc_path = self.get_file_path("Enter document path")
                if username and doc_path:
                    print(f"\n🔍 Verifying signature for: {doc_path}")
                    if self.handle_pki_operation(verify_signature, username, doc_path):
                        print("✅ Signature verified successfully!")
            
            elif choice == 3:
                break
            
            else:
                print("❌ Invalid choice. Please select 1-3.")

    def encryption_operations(self):
        """Handle encryption operations"""
        while True:
            self.display_encryption_menu()
            choice = self.get_user_input("Select option (1-5)", int)

            if choice == 1:
                username = self.get_user_input("Enter recipient username")
                doc_path = self.get_file_path("Enter document path")
                if username and doc_path:
                    print(f"\n🔐 Encrypting document for: {username}")
                    if self.handle_pki_operation(encrypt_document, username, doc_path):
                        print("✅ Document encrypted successfully!")

            elif choice == 2:
                username = self.get_user_input("Enter your username")
                enc_path = self.get_file_path("Enter encrypted file path")
                if username and enc_path:
                    print(f"\n🔓 Decrypting document...")
                    if self.handle_pki_operation(decrypt_document, username, enc_path):
                        print("✅ Document decrypted successfully!")

            elif choice == 3:
                signer = self.get_user_input("Enter signer username")
                recipient = self.get_user_input("Enter recipient username")
                doc_path = self.get_file_path("Enter document path")
                if signer and recipient and doc_path:
                    print(f"\n🔐✍️  Encrypting and signing document...")
                    if self.handle_pki_operation(encrypt_and_sign_document, signer, recipient, doc_path):
                        print("✅ Document encrypted and signed successfully!")

            elif choice == 4:
                signer = self.get_user_input("Enter signer username")
                recipient = self.get_user_input("Enter recipient username")
                enc_path = self.get_file_path("Enter encrypted file path")
                if signer and recipient and enc_path:
                    print(f"\n🔍🔓 Verifying and decrypting document...")
                    if self.handle_pki_operation(verify_and_decrypt_document, signer, recipient, enc_path):
                        print("✅ Document verified and decrypted successfully!")

            elif choice == 5:
                break

            else:
                print("❌ Invalid choice. Please select 1-5.")

    def verification_operations(self):
        """Handle verification and info operations"""
        while True:
            self.display_verification_menu()
            choice = self.get_user_input("Select option (1-5)", int)

            if choice == 1:
                username = self.get_user_input("Enter username")
                if username:
                    cert_path = f"{username}_cert.pem"
                    print(f"\n🔍 Verifying certificate for: {username}")
                    info = self.handle_pki_operation(get_certificate_info, cert_path)
                    if info:
                        print("✅ Certificate verified!")

            elif choice == 2:
                username = self.get_user_input("Enter username")
                if username:
                    cert_path = f"{username}_cert.pem"
                    print(f"\n📜 Getting certificate details for: {username}")
                    info = self.handle_pki_operation(get_certificate_info, cert_path)
                    if info:
                        print("✅ Certificate details retrieved!")

            elif choice == 3:
                username = self.get_user_input("Enter username")
                if username:
                    key_path = f"{username}_key.pem"
                    print(f"\n🔑 Getting key details for: {username}")
                    info = self.handle_pki_operation(get_key_info, key_path)
                    if info:
                        print("✅ Key details retrieved!")

            elif choice == 4:
                print("\n🔍 Verifying PKI setup...")
                status = self.handle_pki_operation(verify_pki_setup)
                if status:
                    print("✅ PKI setup verification completed!")

            elif choice == 5:
                break

            else:
                print("❌ Invalid choice. Please select 1-5.")

    def security_operations(self):
        """Handle security testing operations"""
        while True:
            self.display_security_menu()
            choice = self.get_user_input("Select option (1-6)", int)

            if choice == 1:
                print("\n🛡️  Running all attack simulations...")
                simulator = AttackSimulator()
                signed_doc = self.get_file_path("Enter signed document path", must_exist=False)
                encrypted_file = self.get_file_path("Enter encrypted file path", must_exist=False)
                if signed_doc and encrypted_file:
                    simulator.run_all_attacks(signed_doc, encrypted_file)
                    print("✅ Attack simulations completed!")

            elif choice == 2:
                print("\n🛡️  Running document tampering test...")
                doc_path = self.get_file_path("Enter document path")
                if doc_path:
                    simulator = AttackSimulator()
                    simulator.document_tampering_attack(doc_path)
                    print("✅ Document tampering test completed!")

            elif choice == 3:
                print("\n🛡️  Running signature spoofing test...")
                username = self.get_user_input("Enter username")
                doc_path = self.get_file_path("Enter document path")
                if username and doc_path:
                    simulator = AttackSimulator()
                    simulator.signature_spoofing_attack(username, doc_path)
                    print("✅ Signature spoofing test completed!")

            elif choice == 4:
                print("\n🛡️  Running certificate spoofing test...")
                username = self.get_user_input("Enter target username")
                if username:
                    simulator = AttackSimulator()
                    simulator.certificate_spoofing_attack(username)
                    print("✅ Certificate spoofing test completed!")

            elif choice == 5:
                print("\n🛡️  Running encryption attack test...")
                enc_path = self.get_file_path("Enter encrypted file path")
                if enc_path:
                    simulator = AttackSimulator()
                    simulator.encryption_attack(enc_path)
                    print("✅ Encryption attack test completed!")

            elif choice == 6:
                break

            else:
                print("❌ Invalid choice. Please select 1-6.")

    def reports_operations(self):
        """Handle system reports operations"""
        while True:
            self.display_reports_menu()
            choice = self.get_user_input("Select option (1-4)", int)

            if choice == 1:
                print("\n📊 Generating full system report...")
                report = self.handle_pki_operation(generate_system_report)
                if report:
                    filename = self.get_user_input("Enter report filename (optional)", required=False)
                    if not filename:
                        filename = "pki_system_report.txt"
                    if self.handle_pki_operation(save_report_to_file, report, filename):
                        print(f"✅ System report saved to: {filename}")

            elif choice == 2:
                print("\n📊 Checking PKI setup status...")
                status = self.handle_pki_operation(verify_pki_setup)
                if status:
                    print("✅ PKI setup status retrieved!")

            elif choice == 3:
                print("\n📊 Listing all PKI files...")
                files = self.handle_pki_operation(list_pki_files)
                if files:
                    print("✅ File listing completed!")

            elif choice == 4:
                break

            else:
                print("❌ Invalid choice. Please select 1-4.")

    def run(self):
        """Run the CLI interface"""
        self.display_banner()
        
        while self.running:
            try:
                self.display_main_menu()
                choice = self.get_user_input("Select option (1-9)", int)
                
                if choice == 1:
                    self.setup_operations()
                elif choice == 2:
                    self.user_operations()
                elif choice == 3:
                    self.document_operations()
                elif choice == 4:
                    self.encryption_operations()
                elif choice == 5:
                    self.verification_operations()
                elif choice == 6:
                    self.security_operations()
                elif choice == 7:
                    self.reports_operations()
                elif choice == 8:
                    self.show_help()
                elif choice == 9:
                    print("\n👋 Thank you for using PKI Document Signing System!")
                    self.running = False
                else:
                    print("❌ Invalid choice. Please select 1-9.")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                self.running = False
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
    
    def show_help(self):
        """Display help information"""
        print("\n❓ HELP - PKI Document Signing System")
        print("="*50)
        print("This system provides secure document signing using PKI.")
        print("\nKey Features:")
        print("• Certificate Authority (CA) management")
        print("• User registration with digital certificates")
        print("• Document signing and verification")
        print("• Hybrid encryption (RSA + AES)")
        print("• Security testing and attack simulation")
        print("• Comprehensive system reporting")
        print("\nFor detailed documentation, see the project README.")


def main():
    """Main entry point for CLI"""
    parser = argparse.ArgumentParser(description="PKI Document Signing System CLI")
    parser.add_argument("--version", action="version", version="PKI System v1.0")
    
    args = parser.parse_args()
    
    # Start CLI interface
    cli = PKICLIInterface()
    cli.run()


if __name__ == "__main__":
    main()
