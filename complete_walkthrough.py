#!/usr/bin/env python3
"""
Complete PKI System Walkthrough
Automated demonstration of all system features
"""

import os
import time
from datetime import datetime

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*70}")
    print(f"  {title}")
    print(f"{'='*70}")

def print_step(step_num, title):
    """Print step header"""
    print(f"\n🔹 STEP {step_num}: {title}")
    print("-" * 60)

def pause(seconds=1):
    """Brief pause for readability"""
    time.sleep(seconds)

def complete_walkthrough():
    """Complete system walkthrough"""
    
    print_header("🚀 SIGNATRIX PKI SYSTEM - COMPLETE WALKTHROUGH")
    print("This walkthrough demonstrates all features of the PKI Document Signing System")
    print(f"Walkthrough started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Clean up any existing files
    print("\n🧹 Cleaning up previous files...")
    cleanup_files = ["*.pem", "*.sig", "*.enc", "*.json", "*.txt", "*.meta"]
    import glob
    for pattern in cleanup_files:
        for file in glob.glob(pattern):
            try:
                if not file.endswith('.py'):
                    os.remove(file)
            except:
                pass
    print("✅ Environment cleaned")
    
    pause()
    
    # Step 1: Initialize PKI Infrastructure
    print_step(1, "PKI INFRASTRUCTURE SETUP")
    
    try:
        from ca import create_ca
        print("🏗️  Creating Certificate Authority...")
        create_ca()
        print("✅ Certificate Authority created successfully!")
        print("   📁 CA Certificate: ca_cert.pem")
        print("   🔑 CA Private Key: ca_key.pem")
        
        # Verify CA files
        if os.path.exists("ca_cert.pem") and os.path.exists("ca_key.pem"):
            print("✅ CA files verified and ready")
        else:
            print("❌ CA files missing!")
            return
            
    except Exception as e:
        print(f"❌ CA creation failed: {e}")
        return
    
    pause()
    
    # Step 2: User Management
    print_step(2, "USER REGISTRATION & MANAGEMENT")
    
    users = [
        ("alice", "<EMAIL>", "TechCorp Inc"),
        ("bob", "<EMAIL>", "Design Studio LLC"),
        ("charlie", "<EMAIL>", "Legal Associates"),
        ("diana", "<EMAIL>", "Consulting Group"),
        ("eve", "<EMAIL>", "Innovation Startup")
    ]
    
    try:
        from user import register_user, authenticate_user, get_user_info, list_users
        
        print("👥 Registering users with digital certificates...")
        
        registered_users = []
        for username, email, org in users:
            print(f"\n   👤 Registering: {username}")
            user_info = register_user(username, email, org)
            registered_users.append(username)
            
            print(f"      ✅ User ID: {user_info['user_id'][:16]}...")
            print(f"      📧 Email: {email}")
            print(f"      🏢 Organization: {org}")
            print(f"      🔑 Private Key: {user_info['private_key_file']}")
            print(f"      📜 Certificate: {user_info['certificate_file']}")
        
        print(f"\n✅ {len(registered_users)} users registered successfully!")
        
        # Test authentication
        print(f"\n🔐 Testing user authentication...")
        for username in registered_users[:3]:  # Test first 3 users
            challenge = f"auth_test_{username}_{int(time.time())}"
            auth_result = authenticate_user(username, challenge)
            
            if auth_result['authenticated']:
                print(f"   ✅ {username}: Authentication successful")
            else:
                print(f"   ❌ {username}: Authentication failed")
        
        # List all users
        all_users = list_users()
        print(f"\n📋 Total users in system: {len(all_users)}")
        
    except Exception as e:
        print(f"❌ User management failed: {e}")
        return
    
    pause()
    
    # Step 3: Document Signing Operations
    print_step(3, "DOCUMENT SIGNING & VERIFICATION")
    
    try:
        from signer import sign_document
        from verifier import verify_signature
        
        # Create various types of documents
        documents = [
            ("business_contract.txt", """BUSINESS SERVICE CONTRACT

Agreement between TechCorp Inc and Design Studio LLC

Services: Web application development and UI/UX design
Duration: 6 months (January 2024 - June 2024)
Value: $150,000
Payment: Monthly installments of $25,000

Terms and Conditions:
- All work to be completed according to specifications
- Regular progress reviews every 2 weeks
- Source code and design assets to be delivered upon completion
- 90-day warranty on all deliverables

This contract is legally binding and governed by applicable laws.

Date: """ + datetime.now().strftime('%Y-%m-%d')),
            
            ("nda_agreement.txt", """NON-DISCLOSURE AGREEMENT

This NDA is between TechCorp Inc and Innovation Startup

Purpose: Discussion of potential partnership and technology sharing

Confidential Information includes:
- Proprietary algorithms and source code
- Business strategies and financial data
- Customer lists and market research
- Technical specifications and documentation

Duration: 5 years from signing date
Jurisdiction: State of California

Both parties agree to maintain strict confidentiality.

Date: """ + datetime.now().strftime('%Y-%m-%d')),
            
            ("employment_offer.txt", """EMPLOYMENT OFFER LETTER

TechCorp Inc
Human Resources Department

Dear Diana,

We are pleased to offer you the position of Senior Consultant.

Position Details:
- Title: Senior Technology Consultant
- Department: Strategic Consulting
- Start Date: February 1, 2024
- Salary: $95,000 annually
- Benefits: Full health, dental, vision, 401k matching

This offer is contingent upon:
- Background check completion
- Reference verification
- Signed employment agreement

Please respond within 7 days to accept this offer.

Sincerely,
HR Department

Date: """ + datetime.now().strftime('%Y-%m-%d'))
        ]
        
        print("📝 Creating and signing documents...")
        
        signed_documents = []
        for doc_name, content in documents:
            print(f"\n   📄 Document: {doc_name}")
            
            # Create document
            with open(doc_name, 'w') as f:
                f.write(content)
            print(f"      ✅ Created: {doc_name}")
            
            # Sign document (different signer for each)
            signers = ["alice", "bob", "charlie"]
            signer = signers[len(signed_documents) % len(signers)]
            
            signature_file = sign_document(signer, doc_name)
            print(f"      ✍️  Signed by: {signer}")
            print(f"      📝 Signature: {signature_file}")
            
            # Verify signature
            verify_signature(signer, doc_name)
            print(f"      ✅ Signature verified successfully")
            
            signed_documents.append((doc_name, signer))
        
        print(f"\n✅ {len(signed_documents)} documents signed and verified!")
        
    except Exception as e:
        print(f"❌ Document signing failed: {e}")
        return
    
    pause()
    
    # Step 4: Encryption & Secure Communication
    print_step(4, "ENCRYPTION & SECURE COMMUNICATION")
    
    try:
        from encryptor import encrypt_document, decrypt_document
        from security_manager import encrypt_for_transmission, decrypt_transmission
        
        # Create confidential documents
        confidential_docs = [
            ("financial_report.txt", """CONFIDENTIAL FINANCIAL REPORT Q4 2023

TechCorp Inc - Internal Use Only

Revenue: $2,450,000 (15% increase from Q3)
Expenses: $1,890,000
Net Profit: $560,000
Cash Flow: Positive $340,000

Key Metrics:
- Customer acquisition cost: $125
- Customer lifetime value: $2,800
- Monthly recurring revenue: $185,000
- Churn rate: 3.2%

Strategic Initiatives:
- Expansion into European markets
- New product line development
- Partnership with major enterprise clients

This information is strictly confidential and proprietary."""),
            
            ("technical_specs.txt", """TECHNICAL SPECIFICATIONS - PROJECT ALPHA

Classification: Confidential

System Architecture:
- Microservices-based design
- Kubernetes orchestration
- PostgreSQL database cluster
- Redis caching layer
- Elasticsearch for search

Security Features:
- OAuth 2.0 authentication
- AES-256 encryption at rest
- TLS 1.3 for data in transit
- Role-based access control
- Audit logging

Performance Requirements:
- 99.9% uptime SLA
- <200ms response time
- Support for 10,000 concurrent users
- Auto-scaling capabilities

This document contains proprietary technical information.""")
        ]
        
        print("🔐 Testing document encryption...")
        
        for doc_name, content in confidential_docs:
            print(f"\n   📄 Processing: {doc_name}")
            
            # Create document
            with open(doc_name, 'w') as f:
                f.write(content)
            
            # Encrypt for different users
            recipient = "bob" if "financial" in doc_name else "diana"
            encrypted_file = encrypt_document(recipient, doc_name)
            print(f"      🔐 Encrypted for: {recipient}")
            print(f"      📁 Encrypted file: {encrypted_file}")
            
            # Decrypt document
            decrypted_file = decrypt_document(recipient, encrypted_file)
            print(f"      🔓 Decrypted file: {decrypted_file}")
            
            # Verify integrity
            with open(doc_name, 'r') as f:
                original = f.read()
            with open(decrypted_file, 'r') as f:
                decrypted = f.read()
            
            if original == decrypted:
                print(f"      ✅ Content integrity verified")
            else:
                print(f"      ❌ Content integrity check failed")
        
        # Test secure transmission
        print(f"\n📡 Testing secure transmission...")
        test_doc = confidential_docs[0][0]
        
        transmission_package = encrypt_for_transmission(
            "alice", "charlie", test_doc, 
            "Confidential financial data for legal review"
        )
        print(f"   📤 Transmission package created (alice → charlie)")
        
        result = decrypt_transmission("charlie", transmission_package)
        print(f"   📥 Transmission decrypted successfully")
        print(f"   ✅ Verification: {result['verification_results']['overall_valid']}")
        
    except Exception as e:
        print(f"❌ Encryption operations failed: {e}")
        return
    
    pause()
    
    # Step 5: Legal Document Workflow
    print_step(5, "LEGAL DOCUMENT WORKFLOW")
    
    try:
        from legal_document_system import (
            DocumentType, create_legal_document, sign_legal_document, 
            verify_legal_document, get_audit_trail
        )
        
        print("⚖️  Creating legal documents...")
        
        # Create employment contract
        contract_content = """EMPLOYMENT AGREEMENT

This Employment Agreement is entered into between:

EMPLOYER: TechCorp Inc
EMPLOYEE: Eve Johnson

POSITION: Software Engineer
DEPARTMENT: Product Development
START DATE: March 1, 2024
SALARY: $78,000 per year

BENEFITS PACKAGE:
- Health insurance (company pays 80%)
- Dental and vision coverage
- 401(k) with 4% company match
- 3 weeks paid vacation
- $2,000 professional development budget

RESPONSIBILITIES:
- Full-stack web application development
- Code review and mentoring junior developers
- Participation in agile development process
- Contribution to technical documentation

TERMS:
- At-will employment
- Standard confidentiality agreement
- Non-compete clause (1 year, 50-mile radius)
- Intellectual property assignment

This agreement is governed by the laws of California.
"""
        
        legal_doc = create_legal_document(
            creator_username="charlie",  # Lawyer
            doc_type=DocumentType.CONTRACT,
            title="Employment Agreement - Eve Johnson",
            content=contract_content,
            required_signers=["eve", "charlie"],
            witness_required=True,
            notary_required=False
        )
        
        print(f"   ✅ Legal document created")
        print(f"      📄 Title: {legal_doc['title']}")
        print(f"      🆔 ID: {legal_doc['document_id']}")
        print(f"      👤 Creator: {legal_doc['creator']}")
        print(f"      ✍️  Required signers: {legal_doc['required_signers']}")
        print(f"      👁️  Witness required: {legal_doc['witness_required']}")
        
        # Multi-party signing process
        print(f"\n✍️  Multi-party signing process:")
        
        # Employee signs
        print(f"   1. Employee (Eve) signing...")
        eve_result = sign_legal_document("eve", legal_doc['document_id'])
        print(f"      ✅ Status: {eve_result['document_status']}")
        
        # Lawyer signs
        print(f"   2. Lawyer (Charlie) signing...")
        charlie_result = sign_legal_document("charlie", legal_doc['document_id'])
        print(f"      ✅ Status: {charlie_result['document_status']}")
        
        # Witness signs
        print(f"   3. Witness (Alice) signing...")
        witness_result = sign_legal_document("alice", legal_doc['document_id'], "witness")
        print(f"      ✅ Status: {witness_result['document_status']}")
        
        # Verify legal document
        print(f"\n🔍 Verifying legal document...")
        verification = verify_legal_document(legal_doc['document_id'], "charlie")
        print(f"   ✅ Document valid: {verification['overall_valid']}")
        print(f"   📋 Compliance: {verification['compliance_status']}")
        
        # Show audit trail
        audit_trail = get_audit_trail(document_id=legal_doc['document_id'])
        print(f"   📊 Audit entries: {len(audit_trail)}")
        
    except Exception as e:
        print(f"❌ Legal document workflow failed: {e}")
        return
    
    pause()
    
    # Step 6: Security Testing
    print_step(6, "SECURITY & ATTACK RESISTANCE")
    
    try:
        from attack_simulator import AttackSimulator
        
        simulator = AttackSimulator()
        
        print("🛡️  Running comprehensive security tests...")
        
        test_doc = "business_contract.txt"
        
        # Test various attacks
        attacks = [
            ("Document Tampering", lambda: simulator.document_tampering_attack(test_doc)),
            ("Signature Spoofing", lambda: simulator.signature_spoofing_attack("alice", test_doc)),
            ("Certificate Spoofing", lambda: simulator.certificate_spoofing_attack("alice")),
        ]
        
        for attack_name, attack_func in attacks:
            print(f"\n   🔍 Testing {attack_name}...")
            try:
                attack_func()
                # Check if attack was prevented
                last_result = simulator.attack_results[-1]
                if last_result['success']:
                    print(f"      ❌ Attack succeeded (SECURITY ISSUE!)")
                else:
                    print(f"      ✅ Attack prevented successfully")
            except Exception as e:
                print(f"      ✅ Attack blocked by system: {str(e)[:50]}...")
        
        # Security summary
        attacks_prevented = sum(1 for result in simulator.attack_results if not result['success'])
        total_attacks = len(simulator.attack_results)
        security_score = (attacks_prevented / total_attacks) * 100 if total_attacks > 0 else 100
        
        print(f"\n📊 Security Assessment:")
        print(f"   🛡️  Attacks prevented: {attacks_prevented}/{total_attacks}")
        print(f"   📈 Security score: {security_score:.1f}%")
        
        if security_score >= 90:
            print(f"   🎉 Excellent security posture!")
        elif security_score >= 70:
            print(f"   ⚠️  Good security, some improvements needed")
        else:
            print(f"   🚨 Security concerns detected!")
        
    except Exception as e:
        print(f"❌ Security testing failed: {e}")
        return
    
    pause()
    
    # Final Summary
    print_header("🎉 WALKTHROUGH COMPLETE!")
    
    print("Congratulations! You have successfully explored all features of the PKI system.")
    
    print("\n📊 SYSTEM CAPABILITIES DEMONSTRATED:")
    print("✅ Certificate Authority (CA) management")
    print("✅ User registration with digital certificates")
    print("✅ Multi-factor authentication with private key proof")
    print("✅ Document signing with RSA-PSS digital signatures")
    print("✅ Signature verification with certificate validation")
    print("✅ Document encryption with hybrid cryptography")
    print("✅ Secure transmission with integrity verification")
    print("✅ Legal document workflow with multi-party signing")
    print("✅ Comprehensive audit trails")
    print("✅ Security testing and attack resistance")
    
    print("\n📁 FILES CREATED:")
    created_files = []
    for file in os.listdir('.'):
        if file.endswith(('.pem', '.sig', '.enc', '.txt', '.json', '.meta')):
            created_files.append(file)
    
    for file in sorted(created_files):
        print(f"   📄 {file}")
    
    print(f"\n📈 SYSTEM STATISTICS:")
    print(f"   👥 Users registered: {len(registered_users)}")
    print(f"   📝 Documents signed: {len(signed_documents)}")
    print(f"   🔐 Files encrypted: {len(confidential_docs)}")
    print(f"   ⚖️  Legal documents: 1")
    print(f"   🛡️  Security tests: {total_attacks}")
    
    print("\n🚀 NEXT STEPS:")
    print("1. 🖥️  Try the GUI: python signatrix.py")
    print("2. 🧪 Run full tests: python comprehensive_testing.py")
    print("3. ⚡ Run optimized tests: python optimized_test_runner.py")
    print("4. 📖 Read documentation: README.md")
    print("5. 🔧 Explore CLI: python cli.py")
    
    print(f"\n🎯 Walkthrough completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 Your PKI Document Signing System is ready for use!")


def main():
    """Main entry point"""
    try:
        complete_walkthrough()
    except KeyboardInterrupt:
        print("\n\n⚠️  Walkthrough interrupted by user")
    except Exception as e:
        print(f"\n❌ Walkthrough failed: {e}")


if __name__ == "__main__":
    main()
