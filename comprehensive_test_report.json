{"summary": {"total_tests": 17, "passed_tests": 15, "failed_tests": 2, "success_rate": 88.23529411764706}, "test_results": [{"test_name": "Certificate-based Authentication", "success": true, "details": "User authenticated with certificate serial: 322667699669112624708082577414922957264807795154", "timestamp": "2025-07-14T13:48:04.926797+00:00"}, {"test_name": "Private Key Possession Proof", "success": true, "details": "Successfully proved private key possession", "timestamp": "2025-07-14T13:48:05.023068+00:00"}, {"test_name": "Certificate Validation Against CA", "success": true, "details": "Certificate status: active", "timestamp": "2025-07-14T13:48:05.026680+00:00"}, {"test_name": "Document Signing with Private Key", "success": true, "details": "Signature file created: test_contract.txt.sig", "timestamp": "2025-07-14T13:48:05.116759+00:00"}, {"test_name": "Signature Verification with Public Key", "success": true, "details": "Signature verified successfully", "timestamp": "2025-07-14T13:48:05.130666+00:00"}, {"test_name": "Multi-user Document Signing", "success": true, "details": "Multiple users can sign documents", "timestamp": "2025-07-14T13:48:05.202052+00:00"}, {"test_name": "Data Confidentiality (Encryption)", "success": true, "details": "Document encrypted: confidential_data.txt.enc", "timestamp": "2025-07-14T13:48:05.227942+00:00"}, {"test_name": "Data Integrity Verification", "success": true, "details": "Tampering detected successfully", "timestamp": "2025-07-14T13:48:05.336993+00:00"}, {"test_name": "Secure Transmission", "success": true, "details": "Transmission verified: {'signature_valid': True, 'metadata_integrity': True, 'document_integrity': True, 'overall_valid': True}", "timestamp": "2025-07-14T13:48:05.466935+00:00"}, {"test_name": "Secure Key Generation", "success": true, "details": "Generated 2048-bit RSA key pair", "timestamp": "2025-07-14T13:48:05.596795+00:00"}, {"test_name": "Key Storage and Retrieval", "success": true, "details": "Key files exist: private=True, cert=True", "timestamp": "2025-07-14T13:48:05.596795+00:00"}, {"test_name": "Certificate Revocation", "success": true, "details": "Certificate revoked: Testing revocation", "timestamp": "2025-07-14T13:48:05.619037+00:00"}, {"test_name": "Legal Document Creation", "success": false, "details": "create_legal_document() got an unexpected keyword argument 'creator_username'", "timestamp": "2025-07-14T13:48:05.619037+00:00"}, {"test_name": "Document Tampering Resistance", "success": false, "details": "Document tampering was detected and prevented", "timestamp": "2025-07-14T13:48:05.718082+00:00"}, {"test_name": "Signature Spoofing Resistance", "success": true, "details": "Signature spoofing was detected and prevented", "timestamp": "2025-07-14T13:48:05.806819+00:00"}, {"test_name": "Certificate Spoofing Resistance", "success": true, "details": "Certificate spoofing was detected and prevented", "timestamp": "2025-07-14T13:48:05.924991+00:00"}, {"test_name": "Encryption Attack Resistance", "success": true, "details": "Encryption attack was detected and prevented", "timestamp": "2025-07-14T13:48:06.037089+00:00"}], "timestamp": "2025-07-14T13:48:06.037089+00:00"}