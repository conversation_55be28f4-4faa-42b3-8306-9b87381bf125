#!/usr/bin/env python3
"""
Comprehensive Testing & Validation System
Demonstrates all assignment requirements with multi-user scenarios and attack simulations
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime, timezone

# Import all system components
from ca import create_ca
from user import register_user, authenticate_user, get_user_info, list_users
from signer import sign_document
from verifier import verify_signature
from encryptor import encrypt_document, decrypt_document
from security_manager import encrypt_for_transmission, decrypt_transmission
from key_management import revoke_certificate, is_certificate_revoked, get_revocation_list
from legal_document_system import (
    DocumentType, create_legal_document, sign_legal_document, 
    verify_legal_document, get_audit_trail
)
from attack_simulator import AttackSimulator
from error_handler import PKIError


class ComprehensiveTestSuite:
    """
    Comprehensive test suite demonstrating all assignment requirements:
    
    1. User Authentication with digital certificates
    2. Document Signing and Verification
    3. Security Features (confidentiality, integrity, authentication)
    4. Key Management (generation, storage, revocation)
    5. Real-world Use Case (Legal Document Signing)
    6. Attack Resistance Testing
    """
    
    def __init__(self):
        self.test_results = []
        self.test_users = ['alice', 'bob', 'charlie', 'lawyer_smith', 'notary_jones']
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """Set up clean test environment"""
        print("🔧 Setting up test environment...")
        
        # Initialize PKI
        create_ca()
        
        # Register test users with different roles
        user_details = {
            'alice': {'email': '<EMAIL>', 'organization': 'ABC Corp'},
            'bob': {'email': '<EMAIL>', 'organization': 'XYZ Ltd'},
            'charlie': {'email': '<EMAIL>', 'organization': 'DEF Inc'},
            'lawyer_smith': {'email': '<EMAIL>', 'organization': 'Smith & Associates'},
            'notary_jones': {'email': '<EMAIL>', 'organization': 'Public Notary Services'}
        }
        
        for username, details in user_details.items():
            register_user(username, details['email'], details['organization'])
        
        print(f"✅ Test environment ready with {len(self.test_users)} users")
    
    def log_test_result(self, test_name, success, details=""):
        """Log test result"""
        result = {
            'test_name': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def test_user_authentication(self):
        """Test Requirement 1: User Authentication with Digital Certificates"""
        print("\n📋 Testing User Authentication System")
        print("-" * 50)
        
        # Test 1.1: Certificate-based authentication
        try:
            challenge_data = "authentication_test_challenge"
            auth_result = authenticate_user('alice', challenge_data)
            
            success = auth_result['authenticated'] == True
            self.log_test_result(
                "Certificate-based Authentication",
                success,
                f"User authenticated with certificate serial: {auth_result.get('certificate_serial', 'N/A')}"
            )
        except Exception as e:
            self.log_test_result("Certificate-based Authentication", False, str(e))
        
        # Test 1.2: Private key possession proof
        try:
            # Test with valid challenge
            auth_result = authenticate_user('bob', "private_key_challenge_test")
            success = auth_result['authenticated'] == True
            self.log_test_result(
                "Private Key Possession Proof",
                success,
                "Successfully proved private key possession"
            )
        except Exception as e:
            self.log_test_result("Private Key Possession Proof", False, str(e))
        
        # Test 1.3: Certificate validation against CA
        try:
            user_info = get_user_info('charlie')
            success = user_info['status'] == 'active'
            self.log_test_result(
                "Certificate Validation Against CA",
                success,
                f"Certificate status: {user_info['status']}"
            )
        except Exception as e:
            self.log_test_result("Certificate Validation Against CA", False, str(e))
    
    def test_document_signing_verification(self):
        """Test Requirement 2: Document Signing and Verification"""
        print("\n📝 Testing Document Signing and Verification")
        print("-" * 50)
        
        # Create test document
        test_doc = "test_contract.txt"
        with open(test_doc, 'w') as f:
            f.write("This is a test contract between Alice and Bob.\n")
            f.write("Terms and conditions apply.\n")
            f.write("Signed electronically using PKI system.")
        
        # Test 2.1: Document signing with private key
        try:
            signature_path = sign_document('alice', test_doc)
            success = os.path.exists(signature_path)
            self.log_test_result(
                "Document Signing with Private Key",
                success,
                f"Signature file created: {signature_path}"
            )
        except Exception as e:
            self.log_test_result("Document Signing with Private Key", False, str(e))
        
        # Test 2.2: Signature verification with public key
        try:
            verify_signature('alice', test_doc)
            self.log_test_result(
                "Signature Verification with Public Key",
                True,
                "Signature verified successfully"
            )
        except Exception as e:
            self.log_test_result("Signature Verification with Public Key", False, str(e))
        
        # Test 2.3: Multi-user signing scenario
        try:
            # Bob signs the same document
            sign_document('bob', test_doc)
            
            # Verify both signatures exist
            alice_sig_exists = os.path.exists(f"{test_doc}.sig")
            # Note: In real implementation, we'd have separate signature files
            
            self.log_test_result(
                "Multi-user Document Signing",
                alice_sig_exists,
                "Multiple users can sign documents"
            )
        except Exception as e:
            self.log_test_result("Multi-user Document Signing", False, str(e))
    
    def test_security_features(self):
        """Test Requirement 3: Security Features"""
        print("\n🔒 Testing Security Features")
        print("-" * 50)
        
        # Test 3.1: Confidentiality through encryption
        try:
            test_doc = "confidential_data.txt"
            with open(test_doc, 'w') as f:
                f.write("CONFIDENTIAL: This document contains sensitive information.")
            
            encrypted_file = encrypt_document('alice', test_doc)
            success = encrypted_file is not None and os.path.exists(encrypted_file)
            
            self.log_test_result(
                "Data Confidentiality (Encryption)",
                success,
                f"Document encrypted: {encrypted_file}"
            )
        except Exception as e:
            self.log_test_result("Data Confidentiality (Encryption)", False, str(e))
        
        # Test 3.2: Data integrity verification
        try:
            # Create document and sign it
            integrity_doc = "integrity_test.txt"
            with open(integrity_doc, 'w') as f:
                f.write("Original document content for integrity testing.")
            
            sign_document('alice', integrity_doc)
            
            # Verify original document
            verify_signature('alice', integrity_doc)
            
            # Tamper with document
            with open(integrity_doc, 'w') as f:
                f.write("TAMPERED document content - this should fail verification.")
            
            # Try to verify tampered document (should fail)
            try:
                verify_signature('alice', integrity_doc)
                integrity_maintained = False  # Should not reach here
            except:
                integrity_maintained = True  # Expected to fail
            
            self.log_test_result(
                "Data Integrity Verification",
                integrity_maintained,
                "Tampering detected successfully"
            )
        except Exception as e:
            self.log_test_result("Data Integrity Verification", False, str(e))
        
        # Test 3.3: Secure transmission
        try:
            transmission_doc = "transmission_test.txt"
            with open(transmission_doc, 'w') as f:
                f.write("Document for secure transmission testing.")
            
            # Encrypt for transmission
            transmission_package = encrypt_for_transmission('alice', 'bob', transmission_doc, "Test message")
            
            # Decrypt transmission
            result = decrypt_transmission('bob', transmission_package)
            
            success = result['verification_results']['overall_valid']
            self.log_test_result(
                "Secure Transmission",
                success,
                f"Transmission verified: {result['verification_results']}"
            )
        except Exception as e:
            self.log_test_result("Secure Transmission", False, str(e))
    
    def test_key_management(self):
        """Test Requirement 4: Key Management"""
        print("\n🔑 Testing Key Management System")
        print("-" * 50)
        
        # Test 4.1: Secure key generation
        try:
            from key_management import generate_secure_key_pair
            private_key, public_key = generate_secure_key_pair(2048)
            
            success = private_key.key_size == 2048
            self.log_test_result(
                "Secure Key Generation",
                success,
                f"Generated {private_key.key_size}-bit RSA key pair"
            )
        except Exception as e:
            self.log_test_result("Secure Key Generation", False, str(e))
        
        # Test 4.2: Key storage and retrieval
        try:
            user_info = get_user_info('alice')
            key_file_exists = os.path.exists(user_info['private_key_file'])
            cert_file_exists = os.path.exists(user_info['certificate_file'])
            
            success = key_file_exists and cert_file_exists
            self.log_test_result(
                "Key Storage and Retrieval",
                success,
                f"Key files exist: private={key_file_exists}, cert={cert_file_exists}"
            )
        except Exception as e:
            self.log_test_result("Key Storage and Retrieval", False, str(e))
        
        # Test 4.3: Certificate revocation
        try:
            # Revoke Charlie's certificate
            revoke_certificate('charlie', "Testing revocation")
            
            # Check revocation status
            user_info = get_user_info('charlie')
            cert_file = user_info['certificate_file']
            
            with open(cert_file, "rb") as f:
                from cryptography import x509
                cert = x509.load_pem_x509_certificate(f.read())
            
            is_revoked, revocation_info = is_certificate_revoked(cert.serial_number)
            
            self.log_test_result(
                "Certificate Revocation",
                is_revoked,
                f"Certificate revoked: {revocation_info['reason'] if is_revoked else 'Not revoked'}"
            )
        except Exception as e:
            self.log_test_result("Certificate Revocation", False, str(e))
    
    def test_legal_document_use_case(self):
        """Test Requirement 5: Real-world Use Case"""
        print("\n⚖️  Testing Legal Document Signing Use Case")
        print("-" * 50)
        
        # Test 5.1: Legal document creation
        try:
            contract_content = """
            EMPLOYMENT CONTRACT
            
            This agreement is between ABC Corp (Employer) and Alice Johnson (Employee).
            
            Terms:
            1. Position: Software Developer
            2. Salary: $75,000 per year
            3. Start Date: January 1, 2024
            4. Benefits: Health insurance, 401k matching
            
            This contract is governed by the laws of the jurisdiction.
            """
            
            document = create_legal_document(
                creator_username='lawyer_smith',
                doc_type=DocumentType.CONTRACT,
                title='Employment Contract - Alice Johnson',
                content=contract_content,
                required_signers=['alice', 'lawyer_smith'],
                witness_required=True,
                notary_required=False
            )
            
            success = document['document_id'] is not None
            self.log_test_result(
                "Legal Document Creation",
                success,
                f"Document created with ID: {document['document_id']}"
            )
            
            # Store document ID for further tests
            self.legal_doc_id = document['document_id']
            
        except Exception as e:
            self.log_test_result("Legal Document Creation", False, str(e))
            return
        
        # Test 5.2: Multi-party signing workflow
        try:
            # Alice signs the contract
            sign_result1 = sign_legal_document('alice', self.legal_doc_id)
            
            # Lawyer signs the contract
            sign_result2 = sign_legal_document('lawyer_smith', self.legal_doc_id)
            
            # Witness signs
            sign_result3 = sign_legal_document('bob', self.legal_doc_id, 'witness')
            
            success = all([
                sign_result1['document_status'] in ['partially_signed', 'executed'],
                sign_result2['document_status'] in ['partially_signed', 'executed'],
                sign_result3['document_status'] == 'executed'
            ])
            
            self.log_test_result(
                "Multi-party Signing Workflow",
                success,
                f"Final document status: {sign_result3['document_status']}"
            )
        except Exception as e:
            self.log_test_result("Multi-party Signing Workflow", False, str(e))
        
        # Test 5.3: Legal document verification
        try:
            verification_result = verify_legal_document(self.legal_doc_id, 'lawyer_smith')
            
            success = verification_result['overall_valid']
            self.log_test_result(
                "Legal Document Verification",
                success,
                f"Document valid: {success}, Compliance: {verification_result['compliance_status']}"
            )
        except Exception as e:
            self.log_test_result("Legal Document Verification", False, str(e))
        
        # Test 5.4: Audit trail
        try:
            audit_trail = get_audit_trail(document_id=self.legal_doc_id)
            
            success = len(audit_trail) > 0
            self.log_test_result(
                "Legal Document Audit Trail",
                success,
                f"Audit entries: {len(audit_trail)}"
            )
        except Exception as e:
            self.log_test_result("Legal Document Audit Trail", False, str(e))
    
    def test_attack_resistance(self):
        """Test Requirement 6: Attack Resistance"""
        print("\n🛡️  Testing Attack Resistance")
        print("-" * 50)
        
        # Create test files for attack simulation
        attack_doc = "attack_test_document.txt"
        with open(attack_doc, 'w') as f:
            f.write("This document will be used for attack simulation testing.")
        
        sign_document('alice', attack_doc)
        encrypted_file = encrypt_document('bob', attack_doc)
        
        # Run attack simulations
        simulator = AttackSimulator()
        
        # Test 6.1: Document tampering resistance
        try:
            simulator.document_tampering_attack(attack_doc)
            
            # Check if attack was detected (should fail)
            attack_detected = not simulator.attack_results[-1]['success']
            
            self.log_test_result(
                "Document Tampering Resistance",
                attack_detected,
                "Document tampering was detected and prevented"
            )
        except Exception as e:
            self.log_test_result("Document Tampering Resistance", False, str(e))
        
        # Test 6.2: Signature spoofing resistance
        try:
            simulator.signature_spoofing_attack('alice', attack_doc)
            
            # Check if attack was detected (should fail)
            attack_detected = not simulator.attack_results[-1]['success']
            
            self.log_test_result(
                "Signature Spoofing Resistance",
                attack_detected,
                "Signature spoofing was detected and prevented"
            )
        except Exception as e:
            self.log_test_result("Signature Spoofing Resistance", False, str(e))
        
        # Test 6.3: Certificate spoofing resistance
        try:
            simulator.certificate_spoofing_attack('alice')
            
            # Check if attack was detected (should fail)
            attack_detected = not simulator.attack_results[-1]['success']
            
            self.log_test_result(
                "Certificate Spoofing Resistance",
                attack_detected,
                "Certificate spoofing was detected and prevented"
            )
        except Exception as e:
            self.log_test_result("Certificate Spoofing Resistance", False, str(e))
        
        # Test 6.4: Encryption attack resistance
        try:
            if encrypted_file:
                simulator.encryption_attack(encrypted_file)
                
                # Check if attack was detected (should fail)
                attack_detected = not simulator.attack_results[-1]['success']
                
                self.log_test_result(
                    "Encryption Attack Resistance",
                    attack_detected,
                    "Encryption attack was detected and prevented"
                )
        except Exception as e:
            self.log_test_result("Encryption Attack Resistance", False, str(e))
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🚀 Starting Comprehensive PKI System Testing")
        print("=" * 60)
        
        try:
            # Run all test categories
            self.test_user_authentication()
            self.test_document_signing_verification()
            self.test_security_features()
            self.test_key_management()
            self.test_legal_document_use_case()
            self.test_attack_resistance()
            
            # Generate final report
            self.generate_test_report()
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 ASSIGNMENT REQUIREMENTS VALIDATION:")
        print("-" * 40)
        
        requirements = {
            "1. User Authentication": ["Certificate-based Authentication", "Private Key Possession Proof", "Certificate Validation Against CA"],
            "2. Document Signing & Verification": ["Document Signing with Private Key", "Signature Verification with Public Key", "Multi-user Document Signing"],
            "3. Security Features": ["Data Confidentiality (Encryption)", "Data Integrity Verification", "Secure Transmission"],
            "4. Key Management": ["Secure Key Generation", "Key Storage and Retrieval", "Certificate Revocation"],
            "5. Real-world Use Case": ["Legal Document Creation", "Multi-party Signing Workflow", "Legal Document Verification", "Legal Document Audit Trail"],
            "6. Attack Resistance": ["Document Tampering Resistance", "Signature Spoofing Resistance", "Certificate Spoofing Resistance", "Encryption Attack Resistance"]
        }
        
        for req_name, test_names in requirements.items():
            req_results = [r for r in self.test_results if r['test_name'] in test_names]
            req_passed = sum(1 for r in req_results if r['success'])
            req_total = len(req_results)
            
            status = "✅ COMPLETE" if req_passed == req_total else f"⚠️  {req_passed}/{req_total}"
            print(f"{req_name}: {status}")
        
        # Save detailed report
        report_file = "comprehensive_test_report.json"
        import json
        with open(report_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (passed_tests/total_tests)*100
                },
                'test_results': self.test_results,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! PKI System fully meets assignment requirements.")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Review the details above.")


def main():
    """Main entry point for comprehensive testing"""
    test_suite = ComprehensiveTestSuite()
    test_suite.run_comprehensive_tests()


if __name__ == "__main__":
    main()
