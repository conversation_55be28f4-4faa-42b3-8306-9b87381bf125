#!/usr/bin/env python3
"""
Assignment Demonstration Script
Quick demonstration of all assignment requirements
"""

import os
from datetime import datetime

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print formatted section"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

def demonstrate_assignment_requirements():
    """Demonstrate all assignment requirements"""
    
    print_header("PKI DOCUMENT SIGNING SYSTEM - ASSIGNMENT DEMONSTRATION")
    print("This script demonstrates all assignment requirements")
    print(f"Demonstration started at: {datetime.now()}")
    
    try:
        # Import all required modules
        from ca import create_ca
        from user import register_user, authenticate_user, get_user_info
        from signer import sign_document
        from verifier import verify_signature
        from encryptor import encrypt_document, decrypt_document
        from security_manager import encrypt_for_transmission, decrypt_transmission
        from key_management import revoke_certificate, is_certificate_revoked
        from legal_document_system import (
            DocumentType, create_legal_document, sign_legal_document, 
            verify_legal_document, get_audit_trail
        )
        from attack_simulator import AttackSimulator
        
        print("✅ All modules imported successfully")
        
        # REQUIREMENT 1: USER AUTHENTICATION
        print_section("REQUIREMENT 1: USER AUTHENTICATION")
        
        # Initialize PKI
        print("🏗️  Initializing Certificate Authority...")
        create_ca()
        print("✅ CA initialized")
        
        # Register users
        print("👤 Registering users with digital certificates...")
        register_user("alice", email="<EMAIL>", organization="ABC Corp")
        register_user("bob", email="<EMAIL>", organization="XYZ Ltd")
        register_user("lawyer", email="<EMAIL>", organization="Legal Associates")
        print("✅ Users registered with digital certificates")
        
        # Authenticate user with private key proof
        print("🔐 Authenticating user with private key possession proof...")
        auth_result = authenticate_user("alice", "challenge_data_12345")
        print(f"✅ Authentication result: {auth_result['authenticated']}")
        print(f"   Certificate Serial: {auth_result['certificate_serial']}")
        
        # REQUIREMENT 2: DOCUMENT SIGNING AND VERIFICATION
        print_section("REQUIREMENT 2: DOCUMENT SIGNING AND VERIFICATION")
        
        # Create test document
        test_doc = "business_contract.txt"
        with open(test_doc, 'w') as f:
            f.write("BUSINESS CONTRACT\n")
            f.write("This agreement is between Alice (ABC Corp) and Bob (XYZ Ltd).\n")
            f.write("Terms: Software development services for $50,000.\n")
            f.write("Duration: 6 months starting January 2024.\n")
        
        print(f"📝 Created test document: {test_doc}")
        
        # Sign document
        print("✍️  Signing document with private key...")
        signature_path = sign_document("alice", test_doc)
        print(f"✅ Document signed, signature saved to: {signature_path}")
        
        # Verify signature
        print("🔍 Verifying signature with public key...")
        verify_signature("alice", test_doc)
        print("✅ Signature verified successfully")
        
        # REQUIREMENT 3: SECURITY FEATURES
        print_section("REQUIREMENT 3: SECURITY FEATURES")
        
        # Confidentiality through encryption
        print("🔐 Testing confidentiality through encryption...")
        confidential_doc = "confidential_data.txt"
        with open(confidential_doc, 'w') as f:
            f.write("CONFIDENTIAL: Financial projections and strategic plans.")
        
        encrypted_file = encrypt_document("bob", confidential_doc)
        print(f"✅ Document encrypted for confidentiality: {encrypted_file}")
        
        # Integrity verification
        print("🛡️  Testing data integrity verification...")
        # Document integrity is verified during signature verification
        print("✅ Integrity verification: Document hash checked during verification")
        
        # Secure transmission
        print("📡 Testing secure transmission...")
        transmission_package = encrypt_for_transmission("alice", "bob", test_doc, "Secure business contract")
        result = decrypt_transmission("bob", transmission_package)
        print(f"✅ Secure transmission completed - Verification: {result['verification_results']['overall_valid']}")
        
        # REQUIREMENT 4: KEY MANAGEMENT
        print_section("REQUIREMENT 4: KEY MANAGEMENT")
        
        print("🔑 Demonstrating key management features...")
        
        # Show key generation (already done during user registration)
        user_info = get_user_info("alice")
        print(f"✅ Key Generation: RSA keys generated and stored")
        print(f"   Private Key: {user_info['private_key_file']}")
        print(f"   Certificate: {user_info['certificate_file']}")
        
        # Demonstrate certificate revocation
        print("🚫 Testing certificate revocation...")
        revoke_certificate("bob", "Testing revocation for demonstration")
        
        # Check revocation status
        bob_info = get_user_info("bob")
        with open(bob_info['certificate_file'], "rb") as f:
            from cryptography import x509
            cert = x509.load_pem_x509_certificate(f.read())
        
        is_revoked, revocation_info = is_certificate_revoked(cert.serial_number)
        print(f"✅ Certificate Revocation: Bob's certificate revoked - {revocation_info['reason']}")
        
        # REQUIREMENT 5: REAL-WORLD USE CASE
        print_section("REQUIREMENT 5: REAL-WORLD USE CASE - LEGAL DOCUMENT SIGNING")
        
        print("⚖️  Demonstrating legal document signing system...")
        
        # Create legal contract
        contract_content = """
        EMPLOYMENT CONTRACT
        
        This employment agreement is between ABC Corp (Employer) and Alice Johnson (Employee).
        
        Position: Senior Software Developer
        Salary: $85,000 per year
        Start Date: January 15, 2024
        Benefits: Health insurance, 401k matching, vacation time
        
        This contract is governed by applicable employment laws.
        """
        
        legal_doc = create_legal_document(
            creator_username="lawyer",
            doc_type=DocumentType.CONTRACT,
            title="Employment Contract - Alice Johnson",
            content=contract_content,
            required_signers=["alice", "lawyer"],
            witness_required=True,
            notary_required=False
        )
        
        print(f"✅ Legal document created: {legal_doc['title']}")
        print(f"   Document ID: {legal_doc['document_id']}")
        print(f"   Required signers: {legal_doc['required_signers']}")
        
        # Multi-party signing workflow
        print("✍️  Multi-party signing workflow...")
        
        # Alice signs (employee)
        sign_result1 = sign_legal_document("alice", legal_doc['document_id'])
        print(f"   ✅ Alice signed - Status: {sign_result1['document_status']}")
        
        # Lawyer signs
        sign_result2 = sign_legal_document("lawyer", legal_doc['document_id'])
        print(f"   ✅ Lawyer signed - Status: {sign_result2['document_status']}")
        
        # Re-register Bob for witness (since we revoked his cert)
        register_user("witness_bob", email="<EMAIL>", organization="Witness Services")
        
        # Witness signs
        sign_result3 = sign_legal_document("witness_bob", legal_doc['document_id'], "witness")
        print(f"   ✅ Witness signed - Status: {sign_result3['document_status']}")
        
        # Verify legal document
        print("🔍 Verifying legal document...")
        verification = verify_legal_document(legal_doc['document_id'], "lawyer")
        print(f"✅ Legal document verification: {verification['overall_valid']}")
        print(f"   Compliance status: {verification['compliance_status']}")
        
        # Show audit trail
        audit_trail = get_audit_trail(document_id=legal_doc['document_id'])
        print(f"✅ Audit trail: {len(audit_trail)} entries recorded")
        
        # REQUIREMENT 6: TESTING AND VALIDATION
        print_section("REQUIREMENT 6: ATTACK RESISTANCE TESTING")
        
        print("🛡️  Testing system security against attacks...")
        
        # Create test files for attack simulation
        attack_test_doc = "attack_test.txt"
        with open(attack_test_doc, 'w') as f:
            f.write("This document will be used for security testing.")
        
        sign_document("alice", attack_test_doc)
        encrypted_test = encrypt_document("alice", attack_test_doc)
        
        # Run attack simulations
        simulator = AttackSimulator()
        
        print("   Testing document tampering resistance...")
        simulator.document_tampering_attack(attack_test_doc)
        tampering_prevented = not simulator.attack_results[-1]['success']
        print(f"   ✅ Document tampering {'prevented' if tampering_prevented else 'NOT prevented'}")
        
        print("   Testing signature spoofing resistance...")
        simulator.signature_spoofing_attack("alice", attack_test_doc)
        spoofing_prevented = not simulator.attack_results[-1]['success']
        print(f"   ✅ Signature spoofing {'prevented' if spoofing_prevented else 'NOT prevented'}")
        
        print("   Testing certificate spoofing resistance...")
        simulator.certificate_spoofing_attack("alice")
        cert_spoofing_prevented = not simulator.attack_results[-1]['success']
        print(f"   ✅ Certificate spoofing {'prevented' if cert_spoofing_prevented else 'NOT prevented'}")
        
        if encrypted_test:
            print("   Testing encryption attack resistance...")
            simulator.encryption_attack(encrypted_test)
            encryption_protected = not simulator.attack_results[-1]['success']
            print(f"   ✅ Encryption attack {'prevented' if encryption_protected else 'NOT prevented'}")
        
        # Security summary
        attacks_prevented = sum([tampering_prevented, spoofing_prevented, cert_spoofing_prevented, encryption_protected])
        print(f"\n🛡️  Security Score: {attacks_prevented}/4 attacks prevented")
        
        # FINAL SUMMARY
        print_header("ASSIGNMENT DEMONSTRATION COMPLETE")
        
        print("✅ REQUIREMENT 1: User Authentication - DEMONSTRATED")
        print("   • Digital certificate-based authentication")
        print("   • Private key possession proof")
        print("   • Certificate validation against CA")
        
        print("\n✅ REQUIREMENT 2: Document Signing & Verification - DEMONSTRATED")
        print("   • Document signing with private key")
        print("   • Signature verification with public key")
        print("   • Multi-user signing capabilities")
        
        print("\n✅ REQUIREMENT 3: Security Features - DEMONSTRATED")
        print("   • Confidentiality through encryption")
        print("   • Data integrity verification")
        print("   • Secure transmission with authentication")
        
        print("\n✅ REQUIREMENT 4: Key Management - DEMONSTRATED")
        print("   • Secure key generation and storage")
        print("   • Certificate revocation system")
        print("   • Key lifecycle management")
        
        print("\n✅ REQUIREMENT 5: Real-world Use Case - DEMONSTRATED")
        print("   • Legal document signing system")
        print("   • Multi-party workflow (employee, lawyer, witness)")
        print("   • Compliance verification and audit trail")
        
        print("\n✅ REQUIREMENT 6: Testing & Validation - DEMONSTRATED")
        print("   • Multi-user scenarios")
        print("   • Attack resistance testing")
        print(f"   • Security validation ({attacks_prevented}/4 attacks prevented)")
        
        print(f"\n🎉 ALL ASSIGNMENT REQUIREMENTS SUCCESSFULLY DEMONSTRATED!")
        print(f"\n📱 To explore the full GUI application, run: python signatrix.py")
        print(f"🧪 To run comprehensive tests, run: python comprehensive_testing.py")
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        print("Please ensure all required modules are available.")

if __name__ == "__main__":
    demonstrate_assignment_requirements()
