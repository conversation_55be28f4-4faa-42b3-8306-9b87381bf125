"""
Error Handling Module for PKI Document Signing System
Provides custom exceptions and error handling utilities
"""

import logging
import traceback
from functools import wraps


# Custom Exception Classes
class PKIError(Exception):
    """Base exception for PKI system errors"""
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class CertificateError(PKIError):
    """Exception for certificate-related errors"""
    pass


class SignatureError(PKIError):
    """Exception for signature-related errors"""
    pass


class EncryptionError(PKIError):
    """Exception for encryption-related errors"""
    pass


class KeyError(PKIError):
    """Exception for key-related errors"""
    pass


class FileOperationError(PKIError):
    """Exception for file operation errors"""
    pass


class ValidationError(PKIError):
    """Exception for validation errors"""
    pass


# Error Handler Decorator
def handle_pki_errors(error_type=PKIError, log_errors=True):
    """
    Decorator to handle PKI errors gracefully
    
    Args:
        error_type: Type of PKI error to catch and re-raise
        log_errors: Whether to log errors (default: True)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_type as e:
                if log_errors:
                    logger = logging.getLogger(__name__)
                    logger.error(f"PKI Error in {func.__name__}: {e}")
                raise
            except Exception as e:
                if log_errors:
                    logger = logging.getLogger(__name__)
                    logger.error(f"Unexpected error in {func.__name__}: {e}")
                    logger.debug(traceback.format_exc())
                raise error_type(f"Unexpected error in {func.__name__}: {str(e)}")
        return wrapper
    return decorator


# Error Context Manager
class PKIErrorContext:
    """Context manager for handling PKI operations with proper error handling"""
    
    def __init__(self, operation_name, error_type=PKIError, log_errors=True):
        self.operation_name = operation_name
        self.error_type = error_type
        self.log_errors = log_errors
        self.logger = logging.getLogger(__name__)
    
    def __enter__(self):
        if self.log_errors:
            self.logger.info(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            if self.log_errors:
                self.logger.info(f"Operation completed successfully: {self.operation_name}")
            return True
        
        if issubclass(exc_type, PKIError):
            if self.log_errors:
                self.logger.error(f"PKI Error in {self.operation_name}: {exc_val}")
            return False  # Re-raise PKI errors
        
        # Handle unexpected errors
        if self.log_errors:
            self.logger.error(f"Unexpected error in {self.operation_name}: {exc_val}")
            self.logger.debug(traceback.format_exc())
        
        # Convert to PKI error
        raise self.error_type(f"Operation failed: {self.operation_name} - {str(exc_val)}")


# Validation Functions
def validate_file_path(file_path, must_exist=True):
    """
    Validate file path
    
    Args:
        file_path: Path to validate
        must_exist: Whether file must exist (default: True)
    
    Raises:
        ValidationError: If validation fails
    """
    import os
    
    if not file_path:
        raise ValidationError("File path cannot be empty")
    
    if must_exist and not os.path.exists(file_path):
        raise FileOperationError(f"File not found: {file_path}")
    
    if must_exist and not os.path.isfile(file_path):
        raise FileOperationError(f"Path is not a file: {file_path}")


def validate_username(username):
    """
    Validate username

    Args:
        username: Username to validate

    Raises:
        ValidationError: If validation fails
    """
    if not username:
        raise ValidationError("Username cannot be empty")

    if not isinstance(username, str):
        raise ValidationError("Username must be a string")

    if len(username) < 2:
        raise ValidationError("Username must be at least 2 characters long")

    # Allow alphanumeric characters and underscores for professional usernames
    import re
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        raise ValidationError("Username must contain only alphanumeric characters and underscores")


def validate_certificate_files(username):
    """
    Validate that required certificate files exist for a user
    
    Args:
        username: Username to check
    
    Raises:
        CertificateError: If certificate files are missing
    """
    cert_file = f"{username}_cert.pem"
    key_file = f"{username}_key.pem"
    
    try:
        validate_file_path(cert_file)
        validate_file_path(key_file)
    except FileOperationError as e:
        raise CertificateError(f"Missing certificate files for user {username}: {e}")


# Error Recovery Functions
def safe_file_operation(operation, *args, backup=True, **kwargs):
    """
    Safely perform file operations with backup and recovery
    
    Args:
        operation: Function to execute
        *args: Arguments for the operation
        backup: Whether to create backup (default: True)
        **kwargs: Keyword arguments for the operation
    
    Returns:
        Result of the operation
    
    Raises:
        FileOperationError: If operation fails
    """
    import shutil
    import os
    
    backup_files = []
    
    try:
        # Create backups if requested
        if backup and args:
            for arg in args:
                if isinstance(arg, str) and os.path.exists(arg):
                    backup_path = f"{arg}.backup_temp"
                    shutil.copy2(arg, backup_path)
                    backup_files.append((arg, backup_path))
        
        # Perform operation
        result = operation(*args, **kwargs)
        
        # Clean up backup files on success
        for _, backup_path in backup_files:
            if os.path.exists(backup_path):
                os.remove(backup_path)
        
        return result
        
    except Exception as e:
        # Restore from backups on failure
        for original_path, backup_path in backup_files:
            if os.path.exists(backup_path):
                try:
                    shutil.copy2(backup_path, original_path)
                    os.remove(backup_path)
                except Exception as restore_error:
                    logging.error(f"Failed to restore backup: {restore_error}")
        
        raise FileOperationError(f"File operation failed: {str(e)}")


# Error Reporting
def generate_error_report(error, context=None):
    """
    Generate detailed error report
    
    Args:
        error: Exception object
        context: Additional context information
    
    Returns:
        dict: Error report
    """
    report = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'timestamp': str(logging.Formatter().formatTime(logging.LogRecord(
            name='', level=0, pathname='', lineno=0, msg='', args=(), exc_info=None
        ))),
        'context': context or {}
    }
    
    if isinstance(error, PKIError):
        report['error_code'] = error.error_code
        report['details'] = error.details
    
    if hasattr(error, '__traceback__'):
        report['traceback'] = traceback.format_exception(
            type(error), error, error.__traceback__
        )
    
    return report


def log_error_report(error, context=None, logger=None):
    """
    Log detailed error report
    
    Args:
        error: Exception object
        context: Additional context information
        logger: Logger instance (optional)
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    report = generate_error_report(error, context)
    
    logger.error(f"Error Report: {report['error_type']} - {report['error_message']}")
    if report.get('error_code'):
        logger.error(f"Error Code: {report['error_code']}")
    if report.get('details'):
        logger.error(f"Details: {report['details']}")
    if report.get('context'):
        logger.error(f"Context: {report['context']}")
