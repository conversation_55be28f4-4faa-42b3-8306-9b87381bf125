#!/usr/bin/env python3
"""
Interactive PKI System Setup Guide
Step-by-step setup and usage of the PKI Document Signing System
"""

import os
import sys
from datetime import datetime

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_step(step_num, title):
    """Print step header"""
    print(f"\n📋 STEP {step_num}: {title}")
    print("-" * 50)

def wait_for_user():
    """Wait for user to press Enter"""
    input("\n⏸️  Press Enter to continue...")

def interactive_setup():
    """Interactive setup walkthrough"""
    
    print_header("🚀 SIGNATRIX PKI SYSTEM - INTERACTIVE SETUP")
    print("Welcome to the Signatrix PKI Document Signing System!")
    print("This guide will walk you through setting up and using the system.")
    print(f"Setup started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    wait_for_user()
    
    # Step 1: Initialize PKI
    print_step(1, "INITIALIZE PKI INFRASTRUCTURE")
    print("First, we need to create a Certificate Authority (CA)")
    print("The CA will issue and manage digital certificates for users.")
    
    try:
        from ca import create_ca
        create_ca()
        print("✅ Certificate Authority created successfully!")
        print("   📁 Files created: ca_cert.pem, ca_key.pem")
    except Exception as e:
        print(f"❌ CA creation failed: {e}")
        return
    
    wait_for_user()
    
    # Step 2: Register Users
    print_step(2, "REGISTER USERS")
    print("Now let's register some users with digital certificates.")
    print("Each user will get a private key and certificate signed by the CA.")
    
    users_to_register = [
        ("alice", "<EMAIL>", "ABC Corporation"),
        ("bob", "<EMAIL>", "XYZ Limited"),
        ("charlie", "<EMAIL>", "Legal Associates")
    ]
    
    try:
        from user import register_user
        
        for username, email, org in users_to_register:
            print(f"\n👤 Registering user: {username}")
            user_info = register_user(username, email, org)
            print(f"   ✅ User ID: {user_info['user_id']}")
            print(f"   📧 Email: {email}")
            print(f"   🏢 Organization: {org}")
            print(f"   🔑 Private Key: {user_info['private_key_file']}")
            print(f"   📜 Certificate: {user_info['certificate_file']}")
        
        print(f"\n✅ All {len(users_to_register)} users registered successfully!")
        
    except Exception as e:
        print(f"❌ User registration failed: {e}")
        return
    
    wait_for_user()
    
    # Step 3: Test Authentication
    print_step(3, "TEST USER AUTHENTICATION")
    print("Let's test user authentication with private key possession proof.")
    
    try:
        from user import authenticate_user
        
        test_user = "alice"
        challenge = "authentication_test_challenge_123"
        
        print(f"🔐 Authenticating user: {test_user}")
        print(f"   Challenge data: {challenge}")
        
        auth_result = authenticate_user(test_user, challenge)
        
        if auth_result['authenticated']:
            print("✅ Authentication successful!")
            print(f"   👤 User: {auth_result['username']}")
            print(f"   🆔 User ID: {auth_result['user_id']}")
            print(f"   📜 Certificate Serial: {auth_result['certificate_serial']}")
            print(f"   ⏰ Auth Time: {auth_result['authentication_time']}")
        else:
            print("❌ Authentication failed!")
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return
    
    wait_for_user()
    
    # Step 4: Create and Sign Documents
    print_step(4, "DOCUMENT SIGNING")
    print("Now let's create a document and sign it digitally.")
    
    # Create a sample document
    document_name = "sample_contract.txt"
    document_content = """DIGITAL CONTRACT AGREEMENT

This agreement demonstrates the PKI document signing system.

Parties:
- Alice (ABC Corporation)
- Bob (XYZ Limited)

Terms:
- This is a sample contract for demonstration purposes
- Digital signatures ensure authenticity and non-repudiation
- Document integrity is protected by cryptographic hashes

Date: """ + datetime.now().strftime('%Y-%m-%d') + """

This document will be digitally signed using PKI technology.
"""
    
    try:
        # Create document
        with open(document_name, 'w') as f:
            f.write(document_content)
        
        print(f"📝 Created document: {document_name}")
        print("   Content preview:")
        print("   " + "\n   ".join(document_content.split('\n')[:5]) + "...")
        
        # Sign document
        from signer import sign_document
        
        signer = "alice"
        print(f"\n✍️  Signing document with user: {signer}")
        
        signature_file = sign_document(signer, document_name)
        
        print(f"✅ Document signed successfully!")
        print(f"   📝 Document: {document_name}")
        print(f"   ✍️  Signature: {signature_file}")
        print(f"   👤 Signer: {signer}")
        
    except Exception as e:
        print(f"❌ Document signing failed: {e}")
        return
    
    wait_for_user()
    
    # Step 5: Verify Signatures
    print_step(5, "SIGNATURE VERIFICATION")
    print("Let's verify the digital signature to ensure document authenticity.")
    
    try:
        from verifier import verify_signature
        
        print(f"🔍 Verifying signature for: {document_name}")
        print(f"   Signer: {signer}")
        
        verify_signature(signer, document_name)
        
        print("✅ Signature verification successful!")
        print("   🔒 Document integrity: VERIFIED")
        print("   👤 Signer identity: VERIFIED")
        print("   📜 Certificate validity: VERIFIED")
        
    except Exception as e:
        print(f"❌ Signature verification failed: {e}")
        print("   This could indicate document tampering or invalid signature!")
        return
    
    wait_for_user()
    
    # Step 6: Document Encryption
    print_step(6, "DOCUMENT ENCRYPTION")
    print("Let's encrypt a document for secure storage or transmission.")
    
    try:
        from encryptor import encrypt_document, decrypt_document
        
        # Create confidential document
        confidential_doc = "confidential_data.txt"
        confidential_content = """CONFIDENTIAL INFORMATION

This document contains sensitive business information:

- Financial projections for Q4 2024
- Strategic partnership details
- Proprietary technology specifications
- Employee compensation data

This information is encrypted using hybrid cryptography:
- RSA for key exchange
- AES-256 for data encryption
"""
        
        with open(confidential_doc, 'w') as f:
            f.write(confidential_content)
        
        print(f"📄 Created confidential document: {confidential_doc}")
        
        # Encrypt for Bob
        recipient = "bob"
        print(f"🔐 Encrypting document for: {recipient}")
        
        encrypted_file = encrypt_document(recipient, confidential_doc)
        
        print(f"✅ Document encrypted successfully!")
        print(f"   📄 Original: {confidential_doc}")
        print(f"   🔐 Encrypted: {encrypted_file}")
        print(f"   👤 Recipient: {recipient}")
        
        # Decrypt document
        print(f"\n🔓 Decrypting document as: {recipient}")
        
        decrypted_file = decrypt_document(recipient, encrypted_file)
        
        print(f"✅ Document decrypted successfully!")
        print(f"   🔓 Decrypted: {decrypted_file}")
        
        # Verify content integrity
        with open(confidential_doc, 'r') as f:
            original = f.read()
        with open(decrypted_file, 'r') as f:
            decrypted = f.read()
        
        if original == decrypted:
            print("✅ Content integrity verified - decryption successful!")
        else:
            print("❌ Content integrity check failed!")
        
    except Exception as e:
        print(f"❌ Document encryption failed: {e}")
        return
    
    wait_for_user()
    
    # Step 7: Legal Document Workflow
    print_step(7, "LEGAL DOCUMENT WORKFLOW")
    print("Let's demonstrate a real-world legal document signing workflow.")
    
    try:
        from legal_document_system import DocumentType, create_legal_document, sign_legal_document
        
        # Create legal contract
        print("⚖️  Creating legal employment contract...")
        
        contract_content = """EMPLOYMENT AGREEMENT

This Employment Agreement is entered into between:

EMPLOYER: ABC Corporation
EMPLOYEE: Alice Johnson

POSITION: Senior Software Developer
START DATE: January 15, 2024
SALARY: $85,000 per year

BENEFITS:
- Health insurance coverage
- 401(k) retirement plan with company matching
- 3 weeks paid vacation annually
- Professional development allowance

TERMS:
- This is an at-will employment agreement
- Employee agrees to confidentiality terms
- Standard non-compete clause applies

This agreement is governed by applicable employment laws.
"""
        
        legal_doc = create_legal_document(
            creator_username="charlie",  # Lawyer
            doc_type=DocumentType.CONTRACT,
            title="Employment Agreement - Alice Johnson",
            content=contract_content,
            required_signers=["alice", "charlie"],
            witness_required=False,
            notary_required=False
        )
        
        print(f"✅ Legal document created!")
        print(f"   📄 Title: {legal_doc['title']}")
        print(f"   🆔 Document ID: {legal_doc['document_id']}")
        print(f"   👤 Creator: {legal_doc['creator']}")
        print(f"   ✍️  Required signers: {legal_doc['required_signers']}")
        
        # Multi-party signing
        print(f"\n✍️  Multi-party signing process:")
        
        # Alice signs (employee)
        print("   1. Employee (Alice) signing...")
        alice_result = sign_legal_document("alice", legal_doc['document_id'])
        print(f"      ✅ Alice signed - Status: {alice_result['document_status']}")
        
        # Charlie signs (lawyer)
        print("   2. Lawyer (Charlie) signing...")
        charlie_result = sign_legal_document("charlie", legal_doc['document_id'])
        print(f"      ✅ Charlie signed - Status: {charlie_result['document_status']}")
        
        print(f"\n✅ Legal document workflow completed!")
        print(f"   📄 Final status: {charlie_result['document_status']}")
        
    except Exception as e:
        print(f"❌ Legal document workflow failed: {e}")
        return
    
    wait_for_user()
    
    # Step 8: Security Testing
    print_step(8, "SECURITY TESTING")
    print("Let's test the system's resistance to common attacks.")
    
    try:
        from attack_simulator import AttackSimulator
        
        simulator = AttackSimulator()
        
        print("🛡️  Running security attack simulations...")
        
        # Test document tampering
        print("\n   🔍 Testing document tampering resistance...")
        simulator.document_tampering_attack(document_name)
        
        # Test signature spoofing
        print("   🔍 Testing signature spoofing resistance...")
        simulator.signature_spoofing_attack("alice", document_name)
        
        # Analyze results
        attacks_prevented = sum(1 for result in simulator.attack_results if not result['success'])
        total_attacks = len(simulator.attack_results)
        
        print(f"\n✅ Security test completed!")
        print(f"   🛡️  Attacks prevented: {attacks_prevented}/{total_attacks}")
        print(f"   📊 Security score: {(attacks_prevented/total_attacks)*100:.1f}%")
        
        if attacks_prevented == total_attacks:
            print("   🎉 All attacks successfully prevented!")
        else:
            print("   ⚠️  Some attacks were not prevented - review security measures")
        
    except Exception as e:
        print(f"❌ Security testing failed: {e}")
        return
    
    wait_for_user()
    
    # Final Summary
    print_header("🎉 SETUP COMPLETE!")
    
    print("Congratulations! You have successfully set up and tested the PKI system.")
    print("\n📊 WHAT WE ACCOMPLISHED:")
    print("✅ Created Certificate Authority (CA)")
    print("✅ Registered multiple users with digital certificates")
    print("✅ Tested user authentication with private key proof")
    print("✅ Created and digitally signed documents")
    print("✅ Verified digital signatures")
    print("✅ Encrypted and decrypted confidential documents")
    print("✅ Demonstrated legal document workflow")
    print("✅ Tested security against attacks")
    
    print("\n📁 FILES CREATED:")
    files_created = [
        "ca_cert.pem", "ca_key.pem",
        "alice_key.pem", "alice_cert.pem",
        "bob_key.pem", "bob_cert.pem", 
        "charlie_key.pem", "charlie_cert.pem",
        "sample_contract.txt", "sample_contract.txt.sig",
        "confidential_data.txt", "confidential_data.txt.enc"
    ]
    
    for file in files_created:
        if os.path.exists(file):
            print(f"   📄 {file}")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Explore the GUI: python signatrix.py")
    print("2. Run comprehensive tests: python comprehensive_testing.py")
    print("3. Try the CLI interface: python cli.py")
    print("4. Read documentation: README.md")
    
    print("\n💡 TIP: All your certificates and keys are now ready to use!")
    print("You can sign documents, encrypt files, and verify signatures.")
    
    print(f"\n🎯 Setup completed successfully at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """Main entry point"""
    try:
        interactive_setup()
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("Please check the error and try again.")


if __name__ == "__main__":
    main()
