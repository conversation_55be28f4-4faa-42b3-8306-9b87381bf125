"""
Key Management System for PKI Document Signing System
Implements secure key generation, storage, rotation, and revocation mechanisms
"""

import os
import json
import secrets
import shutil
from datetime import datetime, timezone, timedelta
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography import x509
from error_handler import PKIError, ValidationError, CertificateError


class KeyManagementSystem:
    """Comprehensive key management system with security best practices"""
    
    def __init__(self):
        self.key_registry_file = "key_registry.json"
        self.revocation_list_file = "certificate_revocation_list.json"
        self.key_backup_dir = "key_backups"
        self.load_key_registry()
        self.load_revocation_list()
        self.ensure_backup_directory()
    
    def load_key_registry(self):
        """Load key registry from file"""
        try:
            if os.path.exists(self.key_registry_file):
                with open(self.key_registry_file, 'r') as f:
                    self.key_registry = json.load(f)
            else:
                self.key_registry = {}
        except Exception:
            self.key_registry = {}
    
    def save_key_registry(self):
        """Save key registry to file"""
        try:
            with open(self.key_registry_file, 'w') as f:
                json.dump(self.key_registry, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save key registry: {e}")
    
    def load_revocation_list(self):
        """Load certificate revocation list"""
        try:
            if os.path.exists(self.revocation_list_file):
                with open(self.revocation_list_file, 'r') as f:
                    self.revocation_list = json.load(f)
            else:
                self.revocation_list = []
        except Exception:
            self.revocation_list = []
    
    def save_revocation_list(self):
        """Save certificate revocation list"""
        try:
            with open(self.revocation_list_file, 'w') as f:
                json.dump(self.revocation_list, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save revocation list: {e}")
    
    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        if not os.path.exists(self.key_backup_dir):
            os.makedirs(self.key_backup_dir)
    
    def generate_secure_key_pair(self, key_size=2048):
        """
        Generate a secure RSA key pair with specified key size
        
        Args:
            key_size: RSA key size in bits (default: 2048)
        
        Returns:
            tuple: (private_key, public_key)
        """
        if key_size < 2048:
            raise ValidationError("Key size must be at least 2048 bits for security")
        
        try:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=key_size
            )
            public_key = private_key.public_key()
            
            return private_key, public_key
        except Exception as e:
            raise PKIError(f"Key generation failed: {e}")
    
    def encrypt_private_key(self, private_key, password):
        """
        Encrypt private key with password protection
        
        Args:
            private_key: RSA private key object
            password: Password for encryption
        
        Returns:
            bytes: Encrypted private key in PEM format
        """
        try:
            encrypted_key = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.BestAvailableEncryption(password.encode())
            )
            return encrypted_key
        except Exception as e:
            raise PKIError(f"Private key encryption failed: {e}")
    
    def store_key_securely(self, username, private_key, public_key, password=None):
        """
        Store key pair securely with optional password protection
        
        Args:
            username: Username for the key pair
            private_key: RSA private key object
            public_key: RSA public key object
            password: Optional password for private key encryption
        
        Returns:
            dict: Key storage information
        """
        try:
            key_id = secrets.token_hex(16)
            timestamp = datetime.now(timezone.utc).isoformat()
            
            # Determine file paths
            private_key_file = f"{username}_key.pem"
            public_key_file = f"{username}_public_key.pem"
            
            # Store private key (encrypted if password provided)
            if password:
                encrypted_private_key = self.encrypt_private_key(private_key, password)
                with open(private_key_file, "wb") as f:
                    f.write(encrypted_private_key)
                encryption_status = "password_protected"
            else:
                with open(private_key_file, "wb") as f:
                    f.write(private_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption()
                    ))
                encryption_status = "unencrypted"
            
            # Store public key
            with open(public_key_file, "wb") as f:
                f.write(public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                ))
            
            # Create backup
            self.backup_key_pair(username, private_key_file, public_key_file)
            
            # Register key in registry
            key_info = {
                'key_id': key_id,
                'username': username,
                'key_size': private_key.key_size,
                'creation_time': timestamp,
                'private_key_file': private_key_file,
                'public_key_file': public_key_file,
                'encryption_status': encryption_status,
                'status': 'active',
                'backup_created': True
            }
            
            self.key_registry[key_id] = key_info
            self.save_key_registry()
            
            print(f"[✓] Key pair stored securely for {username} (ID: {key_id})")
            return key_info
            
        except Exception as e:
            raise PKIError(f"Key storage failed: {e}")
    
    def backup_key_pair(self, username, private_key_file, public_key_file):
        """Create backup of key pair"""
        try:
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            backup_subdir = os.path.join(self.key_backup_dir, f"{username}_{timestamp}")
            os.makedirs(backup_subdir, exist_ok=True)
            
            # Copy key files to backup
            if os.path.exists(private_key_file):
                shutil.copy2(private_key_file, os.path.join(backup_subdir, os.path.basename(private_key_file)))
            if os.path.exists(public_key_file):
                shutil.copy2(public_key_file, os.path.join(backup_subdir, os.path.basename(public_key_file)))
            
            print(f"[✓] Key backup created: {backup_subdir}")
            
        except Exception as e:
            print(f"[!] Warning: Key backup failed: {e}")
    
    def rotate_key_pair(self, username, new_key_size=2048, password=None):
        """
        Rotate key pair for a user (generate new keys and revoke old certificate)
        
        Args:
            username: Username for key rotation
            new_key_size: Size for new key pair
            password: Optional password for new private key
        
        Returns:
            dict: Key rotation result
        """
        try:
            # Find existing key info
            existing_key_info = None
            for key_id, info in self.key_registry.items():
                if info['username'] == username and info['status'] == 'active':
                    existing_key_info = info
                    break
            
            if existing_key_info:
                # Mark old key as rotated
                existing_key_info['status'] = 'rotated'
                existing_key_info['rotation_time'] = datetime.now(timezone.utc).isoformat()
                
                # Revoke old certificate if it exists
                cert_file = f"{username}_cert.pem"
                if os.path.exists(cert_file):
                    self.revoke_certificate(username, "Key rotation")
            
            # Generate new key pair
            private_key, public_key = self.generate_secure_key_pair(new_key_size)
            
            # Store new key pair
            new_key_info = self.store_key_securely(username, private_key, public_key, password)
            
            # Update registry
            self.save_key_registry()
            
            result = {
                'username': username,
                'old_key_id': existing_key_info['key_id'] if existing_key_info else None,
                'new_key_id': new_key_info['key_id'],
                'rotation_time': datetime.now(timezone.utc).isoformat(),
                'new_key_size': new_key_size
            }
            
            print(f"[✓] Key pair rotated for {username}")
            return result
            
        except Exception as e:
            raise PKIError(f"Key rotation failed: {e}")
    
    def revoke_certificate(self, username, reason="Unspecified"):
        """
        Revoke a user's certificate and add to CRL
        
        Args:
            username: Username whose certificate to revoke
            reason: Reason for revocation
        """
        try:
            cert_file = f"{username}_cert.pem"
            if not os.path.exists(cert_file):
                raise ValidationError(f"Certificate file not found for {username}")
            
            # Load certificate to get serial number
            with open(cert_file, "rb") as f:
                cert = x509.load_pem_x509_certificate(f.read())
            
            # Add to revocation list
            revocation_entry = {
                'username': username,
                'certificate_serial': str(cert.serial_number),
                'revocation_time': datetime.now(timezone.utc).isoformat(),
                'reason': reason,
                'revoked_by': 'system'  # Could be enhanced to track who revoked
            }
            
            self.revocation_list.append(revocation_entry)
            self.save_revocation_list()
            
            # Update key registry
            for key_id, info in self.key_registry.items():
                if info['username'] == username and info['status'] == 'active':
                    info['status'] = 'revoked'
                    info['revocation_time'] = revocation_entry['revocation_time']
                    info['revocation_reason'] = reason
                    break
            
            self.save_key_registry()
            
            print(f"[✓] Certificate revoked for {username}: {reason}")
            
        except Exception as e:
            raise PKIError(f"Certificate revocation failed: {e}")
    
    def is_certificate_revoked(self, certificate_serial):
        """Check if a certificate is revoked"""
        for entry in self.revocation_list:
            if entry['certificate_serial'] == str(certificate_serial):
                return True, entry
        return False, None
    
    def get_key_info(self, username):
        """Get key information for a user"""
        for key_id, info in self.key_registry.items():
            if info['username'] == username and info['status'] == 'active':
                return info
        return None
    
    def list_active_keys(self):
        """List all active keys"""
        active_keys = []
        for key_id, info in self.key_registry.items():
            if info['status'] == 'active':
                active_keys.append(info)
        return active_keys
    
    def get_revocation_list(self):
        """Get certificate revocation list"""
        return self.revocation_list
    
    def cleanup_old_backups(self, days_to_keep=90):
        """Clean up old key backups"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            if not os.path.exists(self.key_backup_dir):
                return
            
            removed_count = 0
            for backup_dir in os.listdir(self.key_backup_dir):
                backup_path = os.path.join(self.key_backup_dir, backup_dir)
                if os.path.isdir(backup_path):
                    # Extract timestamp from directory name
                    try:
                        timestamp_str = backup_dir.split('_')[-2] + '_' + backup_dir.split('_')[-1]
                        backup_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                        backup_date = backup_date.replace(tzinfo=timezone.utc)
                        
                        if backup_date < cutoff_date:
                            shutil.rmtree(backup_path)
                            removed_count += 1
                    except (ValueError, IndexError):
                        continue  # Skip directories with unexpected naming
            
            print(f"[✓] Cleaned up {removed_count} old key backups")
            
        except Exception as e:
            print(f"[!] Warning: Backup cleanup failed: {e}")


# Global key management system instance
key_manager = KeyManagementSystem()

# Convenience functions
def generate_secure_key_pair(key_size=2048):
    """Generate secure key pair"""
    return key_manager.generate_secure_key_pair(key_size)

def store_key_securely(username, private_key, public_key, password=None):
    """Store key pair securely"""
    return key_manager.store_key_securely(username, private_key, public_key, password)

def rotate_key_pair(username, new_key_size=2048, password=None):
    """Rotate user's key pair"""
    return key_manager.rotate_key_pair(username, new_key_size, password)

def revoke_certificate(username, reason="Unspecified"):
    """Revoke user's certificate"""
    return key_manager.revoke_certificate(username, reason)

def is_certificate_revoked(certificate_serial):
    """Check if certificate is revoked"""
    return key_manager.is_certificate_revoked(certificate_serial)

def get_revocation_list():
    """Get certificate revocation list"""
    return key_manager.get_revocation_list()

def cleanup_old_backups(days_to_keep=90):
    """Clean up old key backups"""
    return key_manager.cleanup_old_backups(days_to_keep)
