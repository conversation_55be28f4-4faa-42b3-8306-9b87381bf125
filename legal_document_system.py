"""
Legal Document Signing System - Real-World Use Case Implementation
Demonstrates practical application of PKI for legal document management
"""

import os
import json
import hashlib
from datetime import datetime, timezone, timedelta
from enum import Enum
from cryptography import x509
from user import authenticate_user, get_user_info
from signer import signing_system
from verifier import verify_signature
from security_manager import encrypt_for_transmission, decrypt_transmission
from key_management import is_certificate_revoked
from error_handler import PKIError, ValidationError


class DocumentType(Enum):
    """Legal document types"""
    CONTRACT = "contract"
    AGREEMENT = "agreement"
    POWER_OF_ATTORNEY = "power_of_attorney"
    WILL = "will"
    DEED = "deed"
    AFFIDAVIT = "affidavit"
    COURT_FILING = "court_filing"
    NOTARIZED_DOCUMENT = "notarized_document"


class DocumentStatus(Enum):
    """Document processing status"""
    DRAFT = "draft"
    PENDING_SIGNATURE = "pending_signature"
    PARTIALLY_SIGNED = "partially_signed"
    FULLY_SIGNED = "fully_signed"
    EXECUTED = "executed"
    ARCHIVED = "archived"


class LegalDocumentSigningSystem:
    """
    Legal Document Signing System
    
    Real-world use case demonstrating PKI application in legal document management.
    This system addresses critical requirements in legal practice:
    
    1. Document Authenticity - Ensures documents are signed by authorized parties
    2. Non-repudiation - Signers cannot deny having signed documents
    3. Integrity - Detects any tampering with signed documents
    4. Audit Trail - Maintains complete record of all document activities
    5. Compliance - Meets legal requirements for electronic signatures
    """
    
    def __init__(self):
        self.documents_db_file = "legal_documents.json"
        self.audit_log_file = "legal_audit_log.json"
        self.load_documents_database()
        self.load_audit_log()
    
    def load_documents_database(self):
        """Load legal documents database"""
        try:
            if os.path.exists(self.documents_db_file):
                with open(self.documents_db_file, 'r') as f:
                    self.documents_db = json.load(f)
            else:
                self.documents_db = {}
        except Exception:
            self.documents_db = {}
    
    def save_documents_database(self):
        """Save legal documents database"""
        try:
            with open(self.documents_db_file, 'w') as f:
                json.dump(self.documents_db, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save documents database: {e}")
    
    def load_audit_log(self):
        """Load audit log"""
        try:
            if os.path.exists(self.audit_log_file):
                with open(self.audit_log_file, 'r') as f:
                    self.audit_log = json.load(f)
            else:
                self.audit_log = []
        except Exception:
            self.audit_log = []
    
    def save_audit_log(self):
        """Save audit log"""
        try:
            with open(self.audit_log_file, 'w') as f:
                json.dump(self.audit_log, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save audit log: {e}")
    
    def log_activity(self, action, document_id, user, details=None):
        """Log activity for audit trail"""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': action,
            'document_id': document_id,
            'user': user,
            'details': details or {},
            'ip_address': '127.0.0.1',  # Could be enhanced to capture real IP
            'session_id': 'local_session'  # Could be enhanced with real session management
        }
        self.audit_log.append(log_entry)
        self.save_audit_log()
    
    def create_legal_document(self, creator_username, document_type, title, content, 
                            required_signers, witness_required=False, notary_required=False):
        """
        Create a new legal document
        
        Args:
            creator_username: Username of document creator
            document_type: Type of legal document (DocumentType)
            title: Document title
            content: Document content
            required_signers: List of usernames who must sign
            witness_required: Whether witness signature is required
            notary_required: Whether notary signature is required
        
        Returns:
            dict: Created document information
        """
        try:
            # Authenticate creator
            auth_result = authenticate_user(creator_username, f"create_document_{datetime.now().timestamp()}")
            if not auth_result['authenticated']:
                raise ValidationError(f"Creator authentication failed")
            
            # Generate document ID
            document_id = hashlib.sha256(f"{creator_username}_{title}_{datetime.now().timestamp()}".encode()).hexdigest()[:16]
            
            # Create document file
            document_filename = f"legal_doc_{document_id}.txt"
            with open(document_filename, 'w', encoding='utf-8') as f:
                f.write(f"LEGAL DOCUMENT\n")
                f.write(f"Title: {title}\n")
                f.write(f"Type: {document_type.value}\n")
                f.write(f"Created by: {creator_username}\n")
                f.write(f"Created on: {datetime.now(timezone.utc).isoformat()}\n")
                f.write(f"Document ID: {document_id}\n")
                f.write(f"\n{'-'*50}\n\n")
                f.write(content)
                f.write(f"\n\n{'-'*50}\n")
                f.write(f"Required Signers: {', '.join(required_signers)}\n")
                if witness_required:
                    f.write(f"Witness Required: Yes\n")
                if notary_required:
                    f.write(f"Notary Required: Yes\n")
            
            # Calculate document hash
            with open(document_filename, 'rb') as f:
                document_hash = hashlib.sha256(f.read()).hexdigest()
            
            # Create document record
            document_record = {
                'document_id': document_id,
                'title': title,
                'type': document_type.value,
                'filename': document_filename,
                'creator': creator_username,
                'creation_time': datetime.now(timezone.utc).isoformat(),
                'status': DocumentStatus.PENDING_SIGNATURE.value,
                'required_signers': required_signers,
                'signatures': {},
                'witness_required': witness_required,
                'notary_required': notary_required,
                'document_hash': document_hash,
                'version': 1
            }
            
            self.documents_db[document_id] = document_record
            self.save_documents_database()
            
            # Log activity
            self.log_activity('document_created', document_id, creator_username, {
                'title': title,
                'type': document_type.value,
                'required_signers': required_signers
            })
            
            print(f"[✓] Legal document created: {title} (ID: {document_id})")
            return document_record
            
        except Exception as e:
            raise PKIError(f"Document creation failed: {e}")
    
    def sign_legal_document(self, signer_username, document_id, signature_type="standard"):
        """
        Sign a legal document with enhanced verification
        
        Args:
            signer_username: Username of the signer
            document_id: ID of document to sign
            signature_type: Type of signature (standard, witness, notary)
        
        Returns:
            dict: Signature result
        """
        try:
            # Verify document exists
            if document_id not in self.documents_db:
                raise ValidationError(f"Document {document_id} not found")
            
            document = self.documents_db[document_id]
            
            # Check if signer is authorized
            if signature_type == "standard" and signer_username not in document['required_signers']:
                raise ValidationError(f"User {signer_username} is not authorized to sign this document")
            
            # Check if already signed by this user
            if signer_username in document['signatures']:
                raise ValidationError(f"Document already signed by {signer_username}")
            
            # Authenticate signer with challenge
            challenge_data = f"sign_legal_document_{document_id}_{signer_username}_{datetime.now().timestamp()}"
            auth_result = authenticate_user(signer_username, challenge_data)
            if not auth_result['authenticated']:
                raise ValidationError(f"Signer authentication failed")
            
            # Check certificate revocation status
            user_info = get_user_info(signer_username)
            cert_file = user_info['certificate_file']
            with open(cert_file, "rb") as f:
                cert = x509.load_pem_x509_certificate(f.read())
            
            is_revoked, revocation_info = is_certificate_revoked(cert.serial_number)
            if is_revoked:
                raise ValidationError(f"Certificate for {signer_username} has been revoked: {revocation_info['reason']}")
            
            # Sign the document
            document_filename = document['filename']
            signature_result = signing_system.sign_document(signer_username, document_filename, require_authentication=False)
            
            # Record signature
            signature_record = {
                'signer': signer_username,
                'signature_type': signature_type,
                'signature_time': datetime.now(timezone.utc).isoformat(),
                'signature_id': signature_result['signature_id'],
                'certificate_serial': str(cert.serial_number),
                'authentication_method': 'private_key_challenge'
            }
            
            document['signatures'][signer_username] = signature_record
            
            # Update document status
            if signature_type == "standard":
                signed_count = len([s for s in document['signatures'].values() if s['signature_type'] == 'standard'])
                required_count = len(document['required_signers'])
                
                if signed_count == required_count:
                    if document['witness_required'] or document['notary_required']:
                        document['status'] = DocumentStatus.PARTIALLY_SIGNED.value
                    else:
                        document['status'] = DocumentStatus.FULLY_SIGNED.value
                else:
                    document['status'] = DocumentStatus.PARTIALLY_SIGNED.value
            
            # Check if document is fully executed
            witness_ok = not document['witness_required'] or any(s['signature_type'] == 'witness' for s in document['signatures'].values())
            notary_ok = not document['notary_required'] or any(s['signature_type'] == 'notary' for s in document['signatures'].values())
            all_signed = len([s for s in document['signatures'].values() if s['signature_type'] == 'standard']) == len(document['required_signers'])
            
            if all_signed and witness_ok and notary_ok:
                document['status'] = DocumentStatus.EXECUTED.value
                document['execution_time'] = datetime.now(timezone.utc).isoformat()
            
            self.save_documents_database()
            
            # Log activity
            self.log_activity('document_signed', document_id, signer_username, {
                'signature_type': signature_type,
                'signature_id': signature_result['signature_id']
            })
            
            print(f"[✓] Legal document signed by {signer_username} ({signature_type})")
            return {
                'document_id': document_id,
                'signer': signer_username,
                'signature_type': signature_type,
                'signature_record': signature_record,
                'document_status': document['status']
            }
            
        except Exception as e:
            raise PKIError(f"Document signing failed: {e}")
    
    def verify_legal_document(self, document_id, verifier_username=None):
        """
        Verify all signatures on a legal document
        
        Args:
            document_id: ID of document to verify
            verifier_username: Optional username of verifier for audit
        
        Returns:
            dict: Verification results
        """
        try:
            if document_id not in self.documents_db:
                raise ValidationError(f"Document {document_id} not found")
            
            document = self.documents_db[document_id]
            document_filename = document['filename']
            
            verification_results = {
                'document_id': document_id,
                'verification_time': datetime.now(timezone.utc).isoformat(),
                'document_integrity': True,
                'signatures_valid': {},
                'overall_valid': True,
                'compliance_status': {}
            }
            
            # Verify document integrity
            try:
                with open(document_filename, 'rb') as f:
                    current_hash = hashlib.sha256(f.read()).hexdigest()
                
                if current_hash != document['document_hash']:
                    verification_results['document_integrity'] = False
                    verification_results['overall_valid'] = False
            except Exception:
                verification_results['document_integrity'] = False
                verification_results['overall_valid'] = False
            
            # Verify each signature
            for signer, sig_record in document['signatures'].items():
                try:
                    # Check certificate revocation
                    is_revoked, _ = is_certificate_revoked(sig_record['certificate_serial'])
                    
                    if is_revoked:
                        verification_results['signatures_valid'][signer] = {
                            'valid': False,
                            'reason': 'Certificate revoked'
                        }
                        verification_results['overall_valid'] = False
                    else:
                        # Verify signature
                        verify_signature(signer, document_filename)
                        verification_results['signatures_valid'][signer] = {
                            'valid': True,
                            'signature_type': sig_record['signature_type'],
                            'signature_time': sig_record['signature_time']
                        }
                except Exception as e:
                    verification_results['signatures_valid'][signer] = {
                        'valid': False,
                        'reason': str(e)
                    }
                    verification_results['overall_valid'] = False
            
            # Check compliance requirements
            required_signatures = set(document['required_signers'])
            actual_signatures = set(s for s, r in document['signatures'].items() if r['signature_type'] == 'standard')
            
            verification_results['compliance_status'] = {
                'all_required_signed': required_signatures.issubset(actual_signatures),
                'witness_requirement_met': not document['witness_required'] or any(r['signature_type'] == 'witness' for r in document['signatures'].values()),
                'notary_requirement_met': not document['notary_required'] or any(r['signature_type'] == 'notary' for r in document['signatures'].values())
            }
            
            # Overall compliance check
            compliance_met = all(verification_results['compliance_status'].values())
            if not compliance_met:
                verification_results['overall_valid'] = False
            
            # Log verification activity
            if verifier_username:
                self.log_activity('document_verified', document_id, verifier_username, {
                    'verification_result': verification_results['overall_valid'],
                    'signatures_checked': len(verification_results['signatures_valid'])
                })
            
            status = "✓" if verification_results['overall_valid'] else "✗"
            print(f"[{status}] Legal document verification completed - Valid: {verification_results['overall_valid']}")
            
            return verification_results
            
        except Exception as e:
            raise PKIError(f"Document verification failed: {e}")
    
    def get_document_info(self, document_id):
        """Get document information"""
        if document_id not in self.documents_db:
            raise ValidationError(f"Document {document_id} not found")
        return self.documents_db[document_id]
    
    def list_documents(self, username=None, status=None):
        """List documents with optional filtering"""
        documents = []
        for doc_id, doc in self.documents_db.items():
            if username and doc['creator'] != username and username not in doc['required_signers']:
                continue
            if status and doc['status'] != status:
                continue
            documents.append(doc)
        return documents
    
    def get_audit_trail(self, document_id=None, username=None):
        """Get audit trail with optional filtering"""
        filtered_log = []
        for entry in self.audit_log:
            if document_id and entry['document_id'] != document_id:
                continue
            if username and entry['user'] != username:
                continue
            filtered_log.append(entry)
        return filtered_log


# Global legal document system instance
legal_system = LegalDocumentSigningSystem()

# Convenience functions
def create_legal_document(creator, doc_type, title, content, signers, witness=False, notary=False):
    """Create legal document"""
    return legal_system.create_legal_document(creator, doc_type, title, content, signers, witness, notary)

def sign_legal_document(signer, document_id, signature_type="standard"):
    """Sign legal document"""
    return legal_system.sign_legal_document(signer, document_id, signature_type)

def verify_legal_document(document_id, verifier=None):
    """Verify legal document"""
    return legal_system.verify_legal_document(document_id, verifier)

def get_document_info(document_id):
    """Get document information"""
    return legal_system.get_document_info(document_id)

def list_documents(username=None, status=None):
    """List documents"""
    return legal_system.list_documents(username, status)

def get_audit_trail(document_id=None, username=None):
    """Get audit trail"""
    return legal_system.get_audit_trail(document_id, username)
