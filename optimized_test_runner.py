#!/usr/bin/env python3
"""
Optimized Test Runner for PKI Document Signing System
Enhanced testing with performance monitoring and optimization
"""

import os
import sys
import time
import json
from datetime import datetime, timezone

# Import optimization module
from performance_optimizer import apply_optimizations, get_performance_stats, system_optimizer

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*70}")
    print(f"  {title}")
    print(f"{'='*70}")

def print_section(title):
    """Print formatted section"""
    print(f"\n{'-'*50}")
    print(f"  {title}")
    print(f"{'-'*50}")

def run_optimized_tests():
    """Run comprehensive tests with optimizations"""
    
    print_header("OPTIMIZED PKI SYSTEM TESTING & VALIDATION")
    print("Enhanced testing with performance monitoring and system optimization")
    print(f"Test session started: {datetime.now(timezone.utc).isoformat()}")
    
    # Step 1: Apply System Optimizations
    print_section("STEP 1: APPLYING SYSTEM OPTIMIZATIONS")
    
    try:
        optimization_report = apply_optimizations()
        print("✅ System optimizations applied successfully")
    except Exception as e:
        print(f"⚠️  Optimization warning: {e}")
        optimization_report = {}
    
    # Step 2: Clean Test Environment
    print_section("STEP 2: PREPARING CLEAN TEST ENVIRONMENT")
    
    # Remove any existing test files
    test_files = [
        "*.pem", "*.sig", "*.enc", "*.json", "*.txt", "*.meta", 
        "*_decrypted.txt", "*.log"
    ]
    
    cleanup_count = 0
    for pattern in test_files:
        try:
            import glob
            files = glob.glob(pattern)
            for file in files:
                if not file.endswith(('.py', '.md', '.bat')):  # Preserve source files
                    try:
                        os.remove(file)
                        cleanup_count += 1
                    except:
                        pass
        except:
            pass
    
    print(f"🧹 Cleaned up {cleanup_count} test files")
    print("✅ Test environment prepared")
    
    # Step 3: Run Core System Tests
    print_section("STEP 3: CORE SYSTEM FUNCTIONALITY TESTS")
    
    test_results = {
        'start_time': datetime.now(timezone.utc).isoformat(),
        'tests': [],
        'performance': {},
        'optimization_report': optimization_report
    }
    
    def log_test(name, success, details="", duration=0):
        """Log test result"""
        result = {
            'name': name,
            'success': success,
            'details': details,
            'duration': duration,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        test_results['tests'].append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        duration_str = f" ({duration:.3f}s)" if duration > 0 else ""
        print(f"  {status}: {name}{duration_str}")
        if details:
            print(f"    {details}")
    
    # Test 1: Module Import Performance
    start_time = time.time()
    try:
        from ca import create_ca
        from user import register_user, authenticate_user, get_user_info
        from signer import sign_document
        from verifier import verify_signature
        from encryptor import encrypt_document, decrypt_document
        from security_manager import encrypt_for_transmission, decrypt_transmission
        from key_management import revoke_certificate, is_certificate_revoked
        from legal_document_system import DocumentType, create_legal_document, sign_legal_document
        from attack_simulator import AttackSimulator
        
        import_duration = time.time() - start_time
        log_test("Module Import Performance", True, "All modules imported", import_duration)
        
    except Exception as e:
        import_duration = time.time() - start_time
        log_test("Module Import Performance", False, str(e), import_duration)
        return test_results
    
    # Test 2: PKI Initialization Performance
    start_time = time.time()
    try:
        create_ca()
        init_duration = time.time() - start_time
        log_test("PKI Initialization", True, "CA created successfully", init_duration)
    except Exception as e:
        init_duration = time.time() - start_time
        log_test("PKI Initialization", False, str(e), init_duration)
    
    # Test 3: User Registration Performance
    test_users = ['alice', 'bob', 'charlie', 'lawyer_smith', 'notary_jones']
    start_time = time.time()
    
    registered_users = 0
    for username in test_users:
        try:
            register_user(username, f"{username}@example.com", f"{username.title()} Corp")
            registered_users += 1
        except Exception as e:
            print(f"    Warning: Failed to register {username}: {e}")
    
    reg_duration = time.time() - start_time
    success = registered_users >= 3  # At least 3 users should register
    log_test("Multi-User Registration", success, 
             f"{registered_users}/{len(test_users)} users registered", reg_duration)
    
    # Test 4: Authentication Performance
    start_time = time.time()
    auth_success = 0
    
    for username in test_users[:3]:  # Test first 3 users
        try:
            auth_result = authenticate_user(username, f"challenge_{username}")
            if auth_result['authenticated']:
                auth_success += 1
        except Exception as e:
            print(f"    Warning: Authentication failed for {username}: {e}")
    
    auth_duration = time.time() - start_time
    log_test("User Authentication", auth_success >= 2, 
             f"{auth_success}/3 authentications successful", auth_duration)
    
    # Test 5: Document Operations Performance
    start_time = time.time()
    
    # Create test document
    test_doc = "performance_test_contract.txt"
    with open(test_doc, 'w') as f:
        f.write("PERFORMANCE TEST CONTRACT\n")
        f.write("This document is used for performance testing.\n")
        f.write("It contains sample contract terms and conditions.\n")
    
    doc_ops_success = 0
    
    # Sign document
    try:
        sign_document('alice', test_doc)
        doc_ops_success += 1
    except Exception as e:
        print(f"    Warning: Signing failed: {e}")
    
    # Verify signature
    try:
        verify_signature('alice', test_doc)
        doc_ops_success += 1
    except Exception as e:
        print(f"    Warning: Verification failed: {e}")
    
    doc_duration = time.time() - start_time
    log_test("Document Operations", doc_ops_success == 2, 
             f"Sign & verify completed", doc_duration)
    
    # Test 6: Encryption Performance
    start_time = time.time()
    
    try:
        encrypted_file = encrypt_document('bob', test_doc)
        decrypted_file = decrypt_document('bob', encrypted_file)
        
        # Verify decrypted content matches original
        with open(test_doc, 'r') as f:
            original = f.read()
        with open(decrypted_file, 'r') as f:
            decrypted = f.read()
        
        encryption_success = original == decrypted
        enc_duration = time.time() - start_time
        log_test("Encryption/Decryption", encryption_success, 
                 "Content integrity verified", enc_duration)
        
    except Exception as e:
        enc_duration = time.time() - start_time
        log_test("Encryption/Decryption", False, str(e), enc_duration)
    
    # Test 7: Legal Document Workflow
    start_time = time.time()
    
    try:
        # Create legal document
        legal_doc = create_legal_document(
            creator_username='lawyer_smith',
            doc_type=DocumentType.CONTRACT,
            title='Performance Test Contract',
            content="This is a test legal contract for performance evaluation.",
            required_signers=['alice', 'lawyer_smith'],
            witness_required=False
        )
        
        # Sign by multiple parties
        sign_legal_document('alice', legal_doc['document_id'])
        sign_legal_document('lawyer_smith', legal_doc['document_id'])
        
        legal_duration = time.time() - start_time
        log_test("Legal Document Workflow", True, 
                 f"Document {legal_doc['document_id']} processed", legal_duration)
        
    except Exception as e:
        legal_duration = time.time() - start_time
        log_test("Legal Document Workflow", False, str(e), legal_duration)
    
    # Test 8: Security Attack Resistance
    start_time = time.time()
    
    try:
        simulator = AttackSimulator()
        
        # Test document tampering
        simulator.document_tampering_attack(test_doc)
        
        # Test signature spoofing
        simulator.signature_spoofing_attack('alice', test_doc)
        
        # Count successful defenses
        attacks_prevented = sum(1 for result in simulator.attack_results if not result['success'])
        total_attacks = len(simulator.attack_results)
        
        security_duration = time.time() - start_time
        security_success = attacks_prevented >= total_attacks * 0.8  # 80% defense rate
        
        log_test("Security Attack Resistance", security_success,
                 f"{attacks_prevented}/{total_attacks} attacks prevented", security_duration)
        
    except Exception as e:
        security_duration = time.time() - start_time
        log_test("Security Attack Resistance", False, str(e), security_duration)
    
    # Step 4: Performance Analysis
    print_section("STEP 4: PERFORMANCE ANALYSIS")
    
    # Calculate overall performance metrics
    total_tests = len(test_results['tests'])
    passed_tests = sum(1 for test in test_results['tests'] if test['success'])
    total_duration = sum(test['duration'] for test in test_results['tests'])
    
    print(f"📊 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests} ✅")
    print(f"   Failed: {total_tests - passed_tests} ❌")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"   Total Duration: {total_duration:.3f}s")
    print(f"   Average Test Duration: {total_duration/total_tests:.3f}s")
    
    # Get performance statistics
    try:
        perf_stats = get_performance_stats()
        test_results['performance'] = perf_stats
        
        if perf_stats:
            print(f"\n🚀 Performance Statistics:")
            for operation, stats in perf_stats.items():
                if stats:
                    print(f"   {operation}: {stats['avg_duration']:.3f}s avg ({stats['count']} calls)")
    except Exception as e:
        print(f"⚠️  Performance stats unavailable: {e}")
    
    # Step 5: Generate Comprehensive Report
    print_section("STEP 5: GENERATING COMPREHENSIVE REPORT")
    
    test_results['end_time'] = datetime.now(timezone.utc).isoformat()
    test_results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': total_tests - passed_tests,
        'success_rate': (passed_tests/total_tests)*100,
        'total_duration': total_duration
    }
    
    # Save detailed report
    report_file = "optimized_test_report.json"
    with open(report_file, 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"📄 Detailed test report saved: {report_file}")
    
    # Generate optimization recommendations
    print(f"\n💡 Optimization Recommendations:")
    if total_duration > 10:
        print(f"   • Consider enabling more aggressive caching")
        print(f"   • Implement parallel processing for batch operations")
    
    if passed_tests < total_tests:
        print(f"   • Review failed tests for system improvements")
        print(f"   • Consider additional error handling")
    
    print(f"   • Monitor performance metrics regularly")
    print(f"   • Update cryptographic libraries for better performance")
    
    # Final Status
    print_header("OPTIMIZED TESTING COMPLETE")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! System is fully optimized and functional.")
    else:
        print(f"⚠️  {total_tests - passed_tests} test(s) failed. Review the report for details.")
    
    print(f"\n🚀 System Performance: {(passed_tests/total_tests)*100:.1f}% success rate")
    print(f"⏱️  Total Test Duration: {total_duration:.3f} seconds")
    print(f"📊 Average Operation Time: {total_duration/total_tests:.3f} seconds")
    
    return test_results


def main():
    """Main entry point"""
    try:
        results = run_optimized_tests()
        
        # Exit with appropriate code
        success_rate = results['summary']['success_rate']
        if success_rate >= 90:
            sys.exit(0)  # Success
        elif success_rate >= 70:
            sys.exit(1)  # Warning
        else:
            sys.exit(2)  # Error
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Testing interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
