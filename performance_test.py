#!/usr/bin/env python3
"""
Performance Test for PKI Document Signing System
Tests the performance of various PKI operations
"""

import time
import os
import tempfile
import shutil
from statistics import mean, median

from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature
from encryptor import encrypt_document, decrypt_document


class PerformanceTest:
    """Performance testing class"""
    
    def __init__(self):
        self.results = {}
        self.test_dir = None
        self.original_dir = None
    
    def setup(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
        
        # Initialize PKI
        create_ca()
        register_user("alice")
        register_user("bob")
        
        # Create test documents of different sizes
        self.create_test_documents()
    
    def teardown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def create_test_documents(self):
        """Create test documents of various sizes"""
        sizes = {
            'small': 1024,      # 1KB
            'medium': 10240,    # 10KB
            'large': 102400,    # 100KB
            'xlarge': 1048576   # 1MB
        }
        
        for size_name, size_bytes in sizes.items():
            filename = f"test_{size_name}.txt"
            with open(filename, "w") as f:
                # Write repeated content to reach desired size
                content = "This is test content for performance testing. " * (size_bytes // 50)
                f.write(content[:size_bytes])
    
    def time_operation(self, operation, *args, **kwargs):
        """Time a single operation"""
        start_time = time.time()
        result = operation(*args, **kwargs)
        end_time = time.time()
        return end_time - start_time, result
    
    def benchmark_operation(self, operation, iterations=5, *args, **kwargs):
        """Benchmark an operation multiple times"""
        times = []
        
        for i in range(iterations):
            duration, result = self.time_operation(operation, *args, **kwargs)
            times.append(duration)
            
            # Clean up signature files for repeated signing tests
            if operation == sign_document and len(args) >= 2:
                sig_file = f"{args[1]}.sig"
                if os.path.exists(sig_file):
                    os.remove(sig_file)
        
        return {
            'times': times,
            'mean': mean(times),
            'median': median(times),
            'min': min(times),
            'max': max(times)
        }
    
    def test_ca_creation_performance(self):
        """Test CA creation performance"""
        print("Testing CA creation performance...")

        def create_ca_clean():
            # Remove existing CA files
            for file in ['ca_cert.pem', 'ca_key.pem']:
                if os.path.exists(file):
                    os.remove(file)
            return create_ca()

        result = self.benchmark_operation(create_ca_clean, iterations=3)
        self.results['ca_creation'] = result

        # Recreate CA and users for subsequent tests
        create_ca()
        register_user("alice")
        register_user("bob")

        print(f"  Mean time: {result['mean']:.3f}s")
        print(f"  Range: {result['min']:.3f}s - {result['max']:.3f}s")
    
    def test_user_registration_performance(self):
        """Test user registration performance"""
        print("Testing user registration performance...")
        
        def register_test_user(username):
            # Clean up existing user files
            for ext in ['_cert.pem', '_key.pem']:
                file = f"{username}{ext}"
                if os.path.exists(file):
                    os.remove(file)
            return register_user(username)
        
        result = self.benchmark_operation(register_test_user, iterations=5, username="testuser")
        self.results['user_registration'] = result
        
        print(f"  Mean time: {result['mean']:.3f}s")
        print(f"  Range: {result['min']:.3f}s - {result['max']:.3f}s")
    
    def test_signing_performance(self):
        """Test document signing performance for different file sizes"""
        print("Testing document signing performance...")
        
        sizes = ['small', 'medium', 'large', 'xlarge']
        
        for size in sizes:
            filename = f"test_{size}.txt"
            result = self.benchmark_operation(sign_document, iterations=5, username="alice", doc_path=filename)
            self.results[f'signing_{size}'] = result
            
            print(f"  {size.capitalize()} file ({os.path.getsize(filename)} bytes):")
            print(f"    Mean time: {result['mean']:.3f}s")
            print(f"    Range: {result['min']:.3f}s - {result['max']:.3f}s")
    
    def test_verification_performance(self):
        """Test signature verification performance"""
        print("Testing signature verification performance...")
        
        sizes = ['small', 'medium', 'large', 'xlarge']
        
        for size in sizes:
            filename = f"test_{size}.txt"
            
            # Sign the document first
            sign_document("alice", filename)
            
            result = self.benchmark_operation(verify_signature, iterations=5, username="alice", doc_path=filename)
            self.results[f'verification_{size}'] = result
            
            print(f"  {size.capitalize()} file ({os.path.getsize(filename)} bytes):")
            print(f"    Mean time: {result['mean']:.3f}s")
            print(f"    Range: {result['min']:.3f}s - {result['max']:.3f}s")
    
    def test_encryption_performance(self):
        """Test encryption performance"""
        print("Testing encryption performance...")
        
        sizes = ['small', 'medium', 'large']  # Skip xlarge for encryption due to time
        
        for size in sizes:
            filename = f"test_{size}.txt"
            
            def encrypt_and_cleanup(username, doc_path):
                result = encrypt_document(username, doc_path)
                # Clean up encrypted file
                if result and os.path.exists(result):
                    os.remove(result)
                return result
            
            result = self.benchmark_operation(encrypt_and_cleanup, iterations=3, username="alice", doc_path=filename)
            self.results[f'encryption_{size}'] = result
            
            print(f"  {size.capitalize()} file ({os.path.getsize(filename)} bytes):")
            print(f"    Mean time: {result['mean']:.3f}s")
            print(f"    Range: {result['min']:.3f}s - {result['max']:.3f}s")
    
    def test_decryption_performance(self):
        """Test decryption performance"""
        print("Testing decryption performance...")
        
        sizes = ['small', 'medium', 'large']
        
        for size in sizes:
            filename = f"test_{size}.txt"
            
            # Encrypt the file first
            encrypted_file = encrypt_document("alice", filename)
            
            def decrypt_and_cleanup(username, enc_path):
                result = decrypt_document(username, enc_path)
                # Clean up decrypted file
                if result and os.path.exists(result):
                    os.remove(result)
                return result
            
            result = self.benchmark_operation(decrypt_and_cleanup, iterations=3, username="alice", enc_path=encrypted_file)
            self.results[f'decryption_{size}'] = result
            
            print(f"  {size.capitalize()} file ({os.path.getsize(filename)} bytes):")
            print(f"    Mean time: {result['mean']:.3f}s")
            print(f"    Range: {result['min']:.3f}s - {result['max']:.3f}s")
            
            # Clean up encrypted file
            if os.path.exists(encrypted_file):
                os.remove(encrypted_file)
    
    def run_all_tests(self):
        """Run all performance tests"""
        print("="*60)
        print("PKI DOCUMENT SIGNING SYSTEM - PERFORMANCE TEST")
        print("="*60)
        
        self.setup()
        
        try:
            self.test_ca_creation_performance()
            print()
            
            self.test_user_registration_performance()
            print()
            
            self.test_signing_performance()
            print()
            
            self.test_verification_performance()
            print()
            
            self.test_encryption_performance()
            print()
            
            self.test_decryption_performance()
            print()
            
            self.print_summary()
            
        finally:
            self.teardown()
    
    def print_summary(self):
        """Print performance summary"""
        print("="*60)
        print("PERFORMANCE SUMMARY")
        print("="*60)
        
        categories = {
            'Setup Operations': ['ca_creation', 'user_registration'],
            'Signing Operations': [k for k in self.results.keys() if k.startswith('signing_')],
            'Verification Operations': [k for k in self.results.keys() if k.startswith('verification_')],
            'Encryption Operations': [k for k in self.results.keys() if k.startswith('encryption_')],
            'Decryption Operations': [k for k in self.results.keys() if k.startswith('decryption_')]
        }
        
        for category, operations in categories.items():
            if operations and all(op in self.results for op in operations):
                print(f"\n{category}:")
                for op in operations:
                    result = self.results[op]
                    print(f"  {op.replace('_', ' ').title()}: {result['mean']:.3f}s avg")
        
        # Overall statistics
        all_times = []
        for result in self.results.values():
            all_times.extend(result['times'])
        
        if all_times:
            print(f"\nOverall Statistics:")
            print(f"  Total operations tested: {len(all_times)}")
            print(f"  Average operation time: {mean(all_times):.3f}s")
            print(f"  Fastest operation: {min(all_times):.3f}s")
            print(f"  Slowest operation: {max(all_times):.3f}s")


def main():
    """Main entry point"""
    test = PerformanceTest()
    test.run_all_tests()


if __name__ == "__main__":
    main()
