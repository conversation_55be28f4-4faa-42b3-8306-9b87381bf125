PKI DOCUMENT SIGNING SYSTEM REPORT
==================================================

Generated: 2025-07-07 23:25:03.422549

PKI SETUP STATUS:
--------------------
CA Certificate: OK
CA Private Key: OK
Registered Users: 2
  - alice: Cert OK, Key OK
  - bob: Cert OK, Key OK

FILE SUMMARY:
---------------
Certificates: 4
  - alice_cert.pem
  - bob_cert.pem
  - ca_cert.pem
  - ca_cert.pem
Private_Keys: 4
  - alice_key.pem
  - bob_key.pem
  - ca_key.pem
  - ca_key.pem
Signatures: 2
  - contract.txt.sig
  - secret_contract.txt.enc.sig
Encrypted_Files: 2
  - confidential.txt.enc
  - secret_contract.txt.enc
Documents: 4
  - confidential.txt
  - contract.txt
  - pki_system_report.txt
  - secret_contract.txt

CERTIFICATE DETAILS:
--------------------
alice_cert.pem:
  Subject: {'countryName': 'NP', 'commonName': 'alice'}
  Issuer: {'countryName': 'NP', 'organizationName': 'SecurePKI Inc.', 'commonName': 'Secure Document Signing CA'}
  Valid From: 2025-07-07 17:40:02
  Valid Until: 2026-07-07 17:40:02
  Key Size: 2048 bits
  Serial: 254294645799636923988910777651859122184394694880

bob_cert.pem:
  Subject: {'countryName': 'NP', 'commonName': 'bob'}
  Issuer: {'countryName': 'NP', 'organizationName': 'SecurePKI Inc.', 'commonName': 'Secure Document Signing CA'}
  Valid From: 2025-07-07 17:40:02
  Valid Until: 2026-07-07 17:40:02
  Key Size: 2048 bits
  Serial: 226892049809928441010597958952100949016397503731

ca_cert.pem:
  Subject: {'countryName': 'NP', 'organizationName': 'SecurePKI Inc.', 'commonName': 'Secure Document Signing CA'}
  Issuer: {'countryName': 'NP', 'organizationName': 'SecurePKI Inc.', 'commonName': 'Secure Document Signing CA'}
  Valid From: 2025-07-07 17:40:02
  Valid Until: 2035-07-05 17:40:02
  Key Size: 2048 bits
  Serial: 3531461849008480269553311705759515699282657847

