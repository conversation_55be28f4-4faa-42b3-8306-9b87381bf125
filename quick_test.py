#!/usr/bin/env python3
"""
Quick PKI System Test
Verify your system is working correctly
"""

def quick_test():
    print("=== PKI SYSTEM STATUS ===")
    
    # Test 1: Check users
    from user import list_users, authenticate_user
    users = list_users()
    print(f"👥 Users: {len(users)} registered")
    for user in users[:3]:
        print(f"   • {user}")
    
    # Test 2: Authentication
    print(f"\n🔐 Testing authentication...")
    auth = authenticate_user('alice', 'test123')
    print(f"   Alice auth: {auth['authenticated']}")
    
    # Test 3: Document signing
    print(f"\n📝 Creating test document...")
    with open('quick_test.txt', 'w') as f:
        f.write('This is a quick test document!')
    
    print(f"✍️  Signing document...")
    from signer import sign_document
    sig_file = sign_document('alice', 'quick_test.txt')
    print(f"   Signature: {sig_file}")
    
    # Test 4: Verification
    print(f"🔍 Verifying signature...")
    from verifier import verify_signature
    verify_signature('alice', 'quick_test.txt')
    print("   ✅ Signature verified!")
    
    print(f"\n🎉 Your PKI system is working perfectly!")

if __name__ == "__main__":
    quick_test()
