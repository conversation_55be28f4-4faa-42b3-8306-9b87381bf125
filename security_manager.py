"""
Advanced Security Manager for PKI Document Signing System
Implements transmission encryption, integrity verification, and comprehensive authentication
"""

import os
import json
import hashlib
import secrets
from datetime import datetime, timezone
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography import x509
from error_handler import PKIError, ValidationError, CertificateError


class SecureTransmissionManager:
    """Manages secure transmission of documents with encryption and integrity verification"""
    
    def __init__(self):
        self.transmission_log_file = "transmission_log.json"
        self.load_transmission_log()
    
    def load_transmission_log(self):
        """Load transmission log from file"""
        try:
            if os.path.exists(self.transmission_log_file):
                with open(self.transmission_log_file, 'r') as f:
                    self.transmission_log = json.load(f)
            else:
                self.transmission_log = []
        except Exception:
            self.transmission_log = []
    
    def save_transmission_log(self):
        """Save transmission log to file"""
        try:
            with open(self.transmission_log_file, 'w') as f:
                json.dump(self.transmission_log, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save transmission log: {e}")
    
    def calculate_integrity_hash(self, data):
        """Calculate SHA-256 hash for integrity verification"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.sha256(data).hexdigest()
    
    def generate_session_key(self):
        """Generate a random session key for symmetric encryption"""
        return secrets.token_bytes(32)  # 256-bit key
    
    def encrypt_for_transmission(self, sender_username, recipient_username, document_path, message=None):
        """
        Encrypt document for secure transmission
        
        Args:
            sender_username: Username of sender
            recipient_username: Username of recipient
            document_path: Path to document to transmit
            message: Optional message to include
        
        Returns:
            dict: Transmission package with encrypted data and metadata
        """
        try:
            # Validate users exist
            from user import get_user_info
            sender_info = get_user_info(sender_username)
            recipient_info = get_user_info(recipient_username)
            
            # Load recipient's certificate for public key
            with open(recipient_info['certificate_file'], "rb") as f:
                recipient_cert = x509.load_pem_x509_certificate(f.read())
            recipient_public_key = recipient_cert.public_key()
            
            # Load sender's private key for signing
            with open(sender_info['private_key_file'], "rb") as f:
                sender_private_key = serialization.load_pem_private_key(f.read(), password=None)
            
            # Read document
            with open(document_path, "rb") as f:
                document_data = f.read()
            
            # Calculate document integrity hash
            document_hash = self.calculate_integrity_hash(document_data)
            
            # Generate session key for symmetric encryption
            session_key = self.generate_session_key()
            iv = os.urandom(16)  # 128-bit IV for AES-CBC
            
            # Encrypt document with AES
            cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv))
            encryptor = cipher.encryptor()
            
            # Pad document data to AES block size
            padding_length = 16 - (len(document_data) % 16)
            padded_data = document_data + bytes([padding_length] * padding_length)
            
            encrypted_document = encryptor.update(padded_data) + encryptor.finalize()
            
            # Encrypt session key with recipient's public key
            encrypted_session_key = recipient_public_key.encrypt(
                session_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Create transmission metadata
            transmission_metadata = {
                'sender': sender_username,
                'recipient': recipient_username,
                'document_name': os.path.basename(document_path),
                'document_hash': document_hash,
                'document_size': len(document_data),
                'transmission_time': datetime.now(timezone.utc).isoformat(),
                'encryption_algorithm': 'AES-256-CBC + RSA-OAEP',
                'message': message or "",
                'transmission_id': secrets.token_hex(16)
            }
            
            # Sign the metadata for authenticity
            metadata_json = json.dumps(transmission_metadata, sort_keys=True)
            metadata_signature = sender_private_key.sign(
                metadata_json.encode(),
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
            
            # Create transmission package
            transmission_package = {
                'metadata': transmission_metadata,
                'metadata_signature': metadata_signature.hex(),
                'encrypted_session_key': encrypted_session_key.hex(),
                'iv': iv.hex(),
                'encrypted_document': encrypted_document.hex(),
                'integrity_verification': {
                    'document_hash': document_hash,
                    'metadata_hash': self.calculate_integrity_hash(metadata_json)
                }
            }
            
            # Log transmission
            log_entry = {
                'transmission_id': transmission_metadata['transmission_id'],
                'sender': sender_username,
                'recipient': recipient_username,
                'document_name': os.path.basename(document_path),
                'timestamp': transmission_metadata['transmission_time'],
                'status': 'encrypted'
            }
            self.transmission_log.append(log_entry)
            self.save_transmission_log()
            
            print(f"[✓] Document encrypted for transmission from {sender_username} to {recipient_username}")
            return transmission_package
            
        except Exception as e:
            raise PKIError(f"Transmission encryption failed: {e}")
    
    def decrypt_transmission(self, recipient_username, transmission_package):
        """
        Decrypt received transmission package
        
        Args:
            recipient_username: Username of recipient
            transmission_package: Encrypted transmission package
        
        Returns:
            dict: Decrypted document and verification results
        """
        try:
            # Load recipient's private key
            from user import get_user_info
            recipient_info = get_user_info(recipient_username)
            
            with open(recipient_info['private_key_file'], "rb") as f:
                recipient_private_key = serialization.load_pem_private_key(f.read(), password=None)
            
            # Load sender's certificate for signature verification
            sender_username = transmission_package['metadata']['sender']
            sender_info = get_user_info(sender_username)
            
            with open(sender_info['certificate_file'], "rb") as f:
                sender_cert = x509.load_pem_x509_certificate(f.read())
            sender_public_key = sender_cert.public_key()
            
            # Verify metadata signature
            metadata_json = json.dumps(transmission_package['metadata'], sort_keys=True)
            metadata_signature = bytes.fromhex(transmission_package['metadata_signature'])
            
            try:
                sender_public_key.verify(
                    metadata_signature,
                    metadata_json.encode(),
                    padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                    hashes.SHA256()
                )
                signature_valid = True
            except Exception:
                signature_valid = False
            
            # Verify metadata integrity
            expected_metadata_hash = transmission_package['integrity_verification']['metadata_hash']
            actual_metadata_hash = self.calculate_integrity_hash(metadata_json)
            metadata_integrity = (expected_metadata_hash == actual_metadata_hash)
            
            # Decrypt session key
            encrypted_session_key = bytes.fromhex(transmission_package['encrypted_session_key'])
            session_key = recipient_private_key.decrypt(
                encrypted_session_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Decrypt document
            iv = bytes.fromhex(transmission_package['iv'])
            encrypted_document = bytes.fromhex(transmission_package['encrypted_document'])
            
            cipher = Cipher(algorithms.AES(session_key), modes.CBC(iv))
            decryptor = cipher.decryptor()
            
            padded_document = decryptor.update(encrypted_document) + decryptor.finalize()
            
            # Remove padding
            padding_length = padded_document[-1]
            document_data = padded_document[:-padding_length]
            
            # Verify document integrity
            expected_doc_hash = transmission_package['integrity_verification']['document_hash']
            actual_doc_hash = self.calculate_integrity_hash(document_data)
            document_integrity = (expected_doc_hash == actual_doc_hash)
            
            # Save decrypted document
            metadata = transmission_package['metadata']
            output_filename = f"received_{metadata['document_name']}"
            with open(output_filename, "wb") as f:
                f.write(document_data)
            
            # Log reception
            log_entry = {
                'transmission_id': metadata['transmission_id'],
                'sender': sender_username,
                'recipient': recipient_username,
                'document_name': metadata['document_name'],
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'status': 'decrypted',
                'verification_results': {
                    'signature_valid': signature_valid,
                    'metadata_integrity': metadata_integrity,
                    'document_integrity': document_integrity
                }
            }
            self.transmission_log.append(log_entry)
            self.save_transmission_log()
            
            result = {
                'decrypted_file': output_filename,
                'document_data': document_data,
                'metadata': metadata,
                'verification_results': {
                    'signature_valid': signature_valid,
                    'metadata_integrity': metadata_integrity,
                    'document_integrity': document_integrity,
                    'overall_valid': signature_valid and metadata_integrity and document_integrity
                }
            }
            
            status = "✓" if result['verification_results']['overall_valid'] else "✗"
            print(f"[{status}] Transmission decrypted from {sender_username} - Verification: {result['verification_results']['overall_valid']}")
            
            return result
            
        except Exception as e:
            raise PKIError(f"Transmission decryption failed: {e}")
    
    def get_transmission_log(self):
        """Get transmission log"""
        return self.transmission_log
    
    def verify_transmission_integrity(self, transmission_package):
        """Verify the integrity of a transmission package without decrypting"""
        try:
            metadata = transmission_package['metadata']
            
            # Verify metadata hash
            metadata_json = json.dumps(metadata, sort_keys=True)
            expected_hash = transmission_package['integrity_verification']['metadata_hash']
            actual_hash = self.calculate_integrity_hash(metadata_json)
            
            return {
                'metadata_integrity': expected_hash == actual_hash,
                'transmission_id': metadata['transmission_id'],
                'sender': metadata['sender'],
                'recipient': metadata['recipient']
            }
        except Exception as e:
            raise PKIError(f"Integrity verification failed: {e}")


# Global security manager instance
security_manager = SecureTransmissionManager()

# Convenience functions
def encrypt_for_transmission(sender, recipient, document_path, message=None):
    """Encrypt document for secure transmission"""
    return security_manager.encrypt_for_transmission(sender, recipient, document_path, message)

def decrypt_transmission(recipient, transmission_package):
    """Decrypt received transmission"""
    return security_manager.decrypt_transmission(recipient, transmission_package)

def get_transmission_log():
    """Get transmission log"""
    return security_manager.get_transmission_log()

def verify_transmission_integrity(transmission_package):
    """Verify transmission integrity"""
    return security_manager.verify_transmission_integrity(transmission_package)
