#!/usr/bin/env python3
"""
Signatrix - GUI Application for PKI Document Signing System
A modern, user-friendly graphical interface for secure document management
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from pathlib import Path

# Import PKI modules
from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature
from encryptor import (
    encrypt_document, decrypt_document,
    encrypt_and_sign_document, verify_and_decrypt_document
)
from attack_simulator import AttackSimulator
from utils import (
    get_certificate_info, get_key_info, verify_pki_setup,
    generate_system_report, save_report_to_file, list_pki_files
)
from error_handler import PKIError


class SignatrixGUI:
    """Main GUI application class for Signatrix"""
    
    def __init__(self, root):
        self.root = root
        self.setup_main_window()
        self.create_widgets()
        self.setup_styles()
        
    def setup_main_window(self):
        """Configure the main window"""
        self.root.title("Signatrix - PKI Document Signing System")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Set icon (if available)
        try:
            self.root.iconbitmap("signatrix.ico")
        except:
            pass  # Icon file not found, continue without it
        
        # Configure grid weights for responsive design
        self.root.grid_rowconfigure(1, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def setup_styles(self):
        """Configure custom styles"""
        style = ttk.Style()
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Info.TLabel', foreground='blue')
    
    def create_widgets(self):
        """Create and arrange all GUI widgets"""
        # Header frame
        self.create_header()
        
        # Main content frame with notebook (tabs)
        self.create_main_content()
        
        # Status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create the header section"""
        header_frame = ttk.Frame(self.root, padding="10")
        header_frame.grid(row=0, column=0, sticky="ew")
        
        # Title
        title_label = ttk.Label(
            header_frame, 
            text="🔐 Signatrix", 
            style='Title.TLabel'
        )
        title_label.grid(row=0, column=0, sticky="w")
        
        # Subtitle
        subtitle_label = ttk.Label(
            header_frame, 
            text="PKI Document Signing System - Secure • Reliable • Professional"
        )
        subtitle_label.grid(row=1, column=0, sticky="w")
        
        # PKI Status indicator
        self.status_frame = ttk.Frame(header_frame)
        self.status_frame.grid(row=0, column=1, rowspan=2, sticky="e")
        
        self.status_label = ttk.Label(self.status_frame, text="PKI Status: Checking...")
        self.status_label.grid(row=0, column=0)
        
        self.refresh_status_btn = ttk.Button(
            self.status_frame, 
            text="Refresh", 
            command=self.check_pki_status
        )
        self.refresh_status_btn.grid(row=0, column=1, padx=(5, 0))
        
        # Configure header grid weights
        header_frame.grid_columnconfigure(0, weight=1)
    
    def create_main_content(self):
        """Create the main content area with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        
        # Create tabs
        self.create_setup_tab()
        self.create_users_tab()
        self.create_documents_tab()
        self.create_encryption_tab()
        self.create_legal_tab()
        self.create_security_tab()
        self.create_reports_tab()
        
        # Initial PKI status check
        self.check_pki_status()
    
    def create_setup_tab(self):
        """Create the Setup & Management tab"""
        setup_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(setup_frame, text="🏗️ Setup")
        
        # CA Management section
        ca_frame = ttk.LabelFrame(setup_frame, text="Certificate Authority", padding="10")
        ca_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Button(
            ca_frame, 
            text="Initialize CA", 
            command=self.initialize_ca,
            width=20
        ).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(
            ca_frame, 
            text="Check CA Status", 
            command=self.check_ca_status,
            width=20
        ).grid(row=0, column=1)
        
        # PKI Files section
        files_frame = ttk.LabelFrame(setup_frame, text="PKI Files Management", padding="10")
        files_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Button(
            files_frame, 
            text="List PKI Files", 
            command=self.list_pki_files,
            width=20
        ).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(
            files_frame, 
            text="Open PKI Directory", 
            command=self.open_pki_directory,
            width=20
        ).grid(row=0, column=1)
        
        # Output area
        output_frame = ttk.LabelFrame(setup_frame, text="Output", padding="10")
        output_frame.grid(row=2, column=0, sticky="nsew", pady=(0, 10))
        
        self.setup_output = scrolledtext.ScrolledText(
            output_frame, 
            height=15, 
            wrap=tk.WORD
        )
        self.setup_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        setup_frame.grid_columnconfigure(0, weight=1)
        setup_frame.grid_rowconfigure(2, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
    
    def create_users_tab(self):
        """Create the User Management tab"""
        users_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(users_frame, text="👤 Users")
        
        # User Registration section
        reg_frame = ttk.LabelFrame(users_frame, text="User Registration", padding="10")
        reg_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(reg_frame, text="Username:").grid(row=0, column=0, sticky="w")
        self.username_entry = ttk.Entry(reg_frame, width=30)
        self.username_entry.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Button(
            reg_frame, 
            text="Register User", 
            command=self.register_user
        ).grid(row=0, column=2)
        
        # User Information section
        info_frame = ttk.LabelFrame(users_frame, text="User Information", padding="10")
        info_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(info_frame, text="Select User:").grid(row=0, column=0, sticky="w")
        self.user_combo = ttk.Combobox(info_frame, width=25, state="readonly")
        self.user_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Button(
            info_frame, 
            text="Get Certificate Info", 
            command=self.get_cert_info
        ).grid(row=0, column=2, padx=(0, 5))
        
        ttk.Button(
            info_frame, 
            text="Get Key Info", 
            command=self.get_key_info
        ).grid(row=0, column=3)
        
        ttk.Button(
            info_frame, 
            text="Refresh Users", 
            command=self.refresh_users
        ).grid(row=1, column=1, pady=(5, 0))
        
        # Output area
        output_frame = ttk.LabelFrame(users_frame, text="Output", padding="10")
        output_frame.grid(row=2, column=0, sticky="nsew")
        
        self.users_output = scrolledtext.ScrolledText(
            output_frame, 
            height=15, 
            wrap=tk.WORD
        )
        self.users_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        users_frame.grid_columnconfigure(0, weight=1)
        users_frame.grid_rowconfigure(2, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
        
        # Initial user list refresh
        self.refresh_users()
    
    def create_documents_tab(self):
        """Create the Document Operations tab"""
        docs_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(docs_frame, text="📝 Documents")
        
        # Document Signing section
        sign_frame = ttk.LabelFrame(docs_frame, text="Document Signing", padding="10")
        sign_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(sign_frame, text="Signer:").grid(row=0, column=0, sticky="w")
        self.sign_user_combo = ttk.Combobox(sign_frame, width=20, state="readonly")
        self.sign_user_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(sign_frame, text="Document:").grid(row=1, column=0, sticky="w", pady=(5, 0))
        self.doc_path_var = tk.StringVar()
        self.doc_path_entry = ttk.Entry(sign_frame, textvariable=self.doc_path_var, width=40)
        self.doc_path_entry.grid(row=1, column=1, padx=(5, 5), pady=(5, 0))
        
        ttk.Button(
            sign_frame, 
            text="Browse", 
            command=self.browse_document
        ).grid(row=1, column=2, pady=(5, 0))
        
        ttk.Button(
            sign_frame, 
            text="Sign Document", 
            command=self.sign_document
        ).grid(row=1, column=3, padx=(5, 0), pady=(5, 0))
        
        # Document Verification section
        verify_frame = ttk.LabelFrame(docs_frame, text="Signature Verification", padding="10")
        verify_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(verify_frame, text="Signer:").grid(row=0, column=0, sticky="w")
        self.verify_user_combo = ttk.Combobox(verify_frame, width=20, state="readonly")
        self.verify_user_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(verify_frame, text="Document:").grid(row=1, column=0, sticky="w", pady=(5, 0))
        self.verify_doc_var = tk.StringVar()
        self.verify_doc_entry = ttk.Entry(verify_frame, textvariable=self.verify_doc_var, width=40)
        self.verify_doc_entry.grid(row=1, column=1, padx=(5, 5), pady=(5, 0))
        
        ttk.Button(
            verify_frame, 
            text="Browse", 
            command=self.browse_verify_document
        ).grid(row=1, column=2, pady=(5, 0))
        
        ttk.Button(
            verify_frame, 
            text="Verify Signature", 
            command=self.verify_document
        ).grid(row=1, column=3, padx=(5, 0), pady=(5, 0))
        
        # Output area
        output_frame = ttk.LabelFrame(docs_frame, text="Output", padding="10")
        output_frame.grid(row=2, column=0, sticky="nsew")
        
        self.docs_output = scrolledtext.ScrolledText(
            output_frame, 
            height=12, 
            wrap=tk.WORD
        )
        self.docs_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        docs_frame.grid_columnconfigure(0, weight=1)
        docs_frame.grid_rowconfigure(2, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
    
    def create_encryption_tab(self):
        """Create the Encryption Operations tab"""
        enc_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(enc_frame, text="🔐 Encryption")
        
        # Basic Encryption section
        basic_frame = ttk.LabelFrame(enc_frame, text="Basic Encryption", padding="10")
        basic_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # Encrypt row
        ttk.Label(basic_frame, text="Recipient:").grid(row=0, column=0, sticky="w")
        self.encrypt_user_combo = ttk.Combobox(basic_frame, width=15, state="readonly")
        self.encrypt_user_combo.grid(row=0, column=1, padx=(5, 10))
        
        self.encrypt_file_var = tk.StringVar()
        self.encrypt_file_entry = ttk.Entry(basic_frame, textvariable=self.encrypt_file_var, width=30)
        self.encrypt_file_entry.grid(row=0, column=2, padx=(0, 5))
        
        ttk.Button(basic_frame, text="Browse", command=self.browse_encrypt_file).grid(row=0, column=3)
        ttk.Button(basic_frame, text="Encrypt", command=self.encrypt_file).grid(row=0, column=4, padx=(5, 0))
        
        # Decrypt row
        ttk.Label(basic_frame, text="Your Username:").grid(row=1, column=0, sticky="w", pady=(5, 0))
        self.decrypt_user_combo = ttk.Combobox(basic_frame, width=15, state="readonly")
        self.decrypt_user_combo.grid(row=1, column=1, padx=(5, 10), pady=(5, 0))
        
        self.decrypt_file_var = tk.StringVar()
        self.decrypt_file_entry = ttk.Entry(basic_frame, textvariable=self.decrypt_file_var, width=30)
        self.decrypt_file_entry.grid(row=1, column=2, padx=(0, 5), pady=(5, 0))
        
        ttk.Button(basic_frame, text="Browse", command=self.browse_decrypt_file).grid(row=1, column=3, pady=(5, 0))
        ttk.Button(basic_frame, text="Decrypt", command=self.decrypt_file).grid(row=1, column=4, padx=(5, 0), pady=(5, 0))
        
        # Advanced Encryption section
        adv_frame = ttk.LabelFrame(enc_frame, text="Encrypt & Sign Workflow", padding="10")
        adv_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(adv_frame, text="Signer:").grid(row=0, column=0, sticky="w")
        self.enc_sign_signer_combo = ttk.Combobox(adv_frame, width=12, state="readonly")
        self.enc_sign_signer_combo.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(adv_frame, text="Recipient:").grid(row=0, column=2, sticky="w")
        self.enc_sign_recipient_combo = ttk.Combobox(adv_frame, width=12, state="readonly")
        self.enc_sign_recipient_combo.grid(row=0, column=3, padx=(5, 10))
        
        self.enc_sign_file_var = tk.StringVar()
        self.enc_sign_file_entry = ttk.Entry(adv_frame, textvariable=self.enc_sign_file_var, width=25)
        self.enc_sign_file_entry.grid(row=0, column=4, padx=(0, 5))
        
        ttk.Button(adv_frame, text="Browse", command=self.browse_enc_sign_file).grid(row=0, column=5)
        ttk.Button(adv_frame, text="Encrypt & Sign", command=self.encrypt_and_sign).grid(row=0, column=6, padx=(5, 0))
        
        # Output area
        output_frame = ttk.LabelFrame(enc_frame, text="Output", padding="10")
        output_frame.grid(row=2, column=0, sticky="nsew")
        
        self.enc_output = scrolledtext.ScrolledText(
            output_frame, 
            height=12, 
            wrap=tk.WORD
        )
        self.enc_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        enc_frame.grid_columnconfigure(0, weight=1)
        enc_frame.grid_rowconfigure(2, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
    
    def create_security_tab(self):
        """Create the Security Testing tab"""
        sec_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(sec_frame, text="🛡️ Security")
        
        # Attack Simulation section
        attack_frame = ttk.LabelFrame(sec_frame, text="Attack Simulation", padding="10")
        attack_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Button(
            attack_frame, 
            text="Run All Attacks", 
            command=self.run_all_attacks,
            width=20
        ).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(
            attack_frame, 
            text="Document Tampering", 
            command=self.test_document_tampering,
            width=20
        ).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(
            attack_frame, 
            text="Signature Spoofing", 
            command=self.test_signature_spoofing,
            width=20
        ).grid(row=0, column=2)
        
        # Test Files section
        files_frame = ttk.LabelFrame(sec_frame, text="Test Files", padding="10")
        files_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Label(files_frame, text="Signed Document:").grid(row=0, column=0, sticky="w")
        self.test_signed_var = tk.StringVar()
        self.test_signed_entry = ttk.Entry(files_frame, textvariable=self.test_signed_var, width=40)
        self.test_signed_entry.grid(row=0, column=1, padx=(5, 5))
        
        ttk.Button(files_frame, text="Browse", command=self.browse_test_signed).grid(row=0, column=2)
        
        ttk.Label(files_frame, text="Encrypted File:").grid(row=1, column=0, sticky="w", pady=(5, 0))
        self.test_encrypted_var = tk.StringVar()
        self.test_encrypted_entry = ttk.Entry(files_frame, textvariable=self.test_encrypted_var, width=40)
        self.test_encrypted_entry.grid(row=1, column=1, padx=(5, 5), pady=(5, 0))
        
        ttk.Button(files_frame, text="Browse", command=self.browse_test_encrypted).grid(row=1, column=2, pady=(5, 0))
        
        # Output area
        output_frame = ttk.LabelFrame(sec_frame, text="Security Test Results", padding="10")
        output_frame.grid(row=2, column=0, sticky="nsew")
        
        self.sec_output = scrolledtext.ScrolledText(
            output_frame, 
            height=12, 
            wrap=tk.WORD
        )
        self.sec_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        sec_frame.grid_columnconfigure(0, weight=1)
        sec_frame.grid_rowconfigure(2, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
    
    def create_reports_tab(self):
        """Create the Reports tab"""
        reports_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(reports_frame, text="📊 Reports")
        
        # Report Generation section
        gen_frame = ttk.LabelFrame(reports_frame, text="Report Generation", padding="10")
        gen_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        ttk.Button(
            gen_frame, 
            text="Generate System Report", 
            command=self.generate_system_report,
            width=25
        ).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(
            gen_frame, 
            text="Check PKI Status", 
            command=self.detailed_pki_status,
            width=25
        ).grid(row=0, column=1)
        
        # Report Display area
        output_frame = ttk.LabelFrame(reports_frame, text="Report Output", padding="10")
        output_frame.grid(row=1, column=0, sticky="nsew")
        
        self.reports_output = scrolledtext.ScrolledText(
            output_frame, 
            height=20, 
            wrap=tk.WORD
        )
        self.reports_output.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        reports_frame.grid_columnconfigure(0, weight=1)
        reports_frame.grid_rowconfigure(1, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)
        output_frame.grid_rowconfigure(0, weight=1)
    
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.grid(row=2, column=0, sticky="ew")
        
        self.status_text = tk.StringVar()
        self.status_text.set("Ready")
        
        status_label = ttk.Label(self.status_bar, textvariable=self.status_text)
        status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # Progress bar (initially hidden)
        self.progress = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress.grid(row=0, column=1, padx=10, pady=5, sticky="e")
        self.progress.grid_remove()  # Hide initially
        
        self.status_bar.grid_columnconfigure(0, weight=1)

    # Utility Methods
    def show_progress(self, show=True):
        """Show or hide progress bar"""
        if show:
            self.progress.grid()
            self.progress.start()
        else:
            self.progress.stop()
            self.progress.grid_remove()

    def update_status(self, message):
        """Update status bar message"""
        self.status_text.set(message)
        self.root.update_idletasks()

    def append_output(self, text_widget, message):
        """Append message to output text widget"""
        text_widget.insert(tk.END, message + "\n")
        text_widget.see(tk.END)
        self.root.update_idletasks()

    def clear_output(self, text_widget):
        """Clear output text widget"""
        text_widget.delete(1.0, tk.END)

    def run_in_thread(self, func, *args, **kwargs):
        """Run function in separate thread to prevent GUI freezing"""
        def wrapper():
            try:
                func(*args, **kwargs)
            except Exception as e:
                messagebox.showerror("Error", f"Operation failed: {str(e)}")
            finally:
                self.show_progress(False)
                self.update_status("Ready")

        thread = threading.Thread(target=wrapper)
        thread.daemon = True
        thread.start()

    def safe_pki_operation(self, operation, output_widget, *args, **kwargs):
        """Safely execute PKI operation with error handling"""
        try:
            result = operation(*args, **kwargs)
            self.append_output(output_widget, f"✅ Operation completed successfully")
            return result
        except PKIError as e:
            self.append_output(output_widget, f"❌ PKI Error: {e}")
            return None
        except Exception as e:
            self.append_output(output_widget, f"❌ Unexpected Error: {e}")
            return None

    # PKI Status Methods
    def check_pki_status(self):
        """Check and update PKI status"""
        try:
            ca_exists = os.path.exists("ca_cert.pem") and os.path.exists("ca_key.pem")
            if ca_exists:
                self.status_label.config(text="PKI Status: ✅ Ready", style='Success.TLabel')
            else:
                self.status_label.config(text="PKI Status: ❌ Not Initialized", style='Error.TLabel')

            # Refresh user lists
            self.refresh_users()

        except Exception as e:
            self.status_label.config(text="PKI Status: ❌ Error", style='Error.TLabel')

    def refresh_users(self):
        """Refresh user combo boxes"""
        try:
            # Find all user certificate files
            users = []
            for file in os.listdir("."):
                if file.endswith("_cert.pem") and not file.startswith("ca_"):
                    username = file.replace("_cert.pem", "")
                    users.append(username)

            # Update all user combo boxes
            combo_boxes = [
                self.user_combo, self.sign_user_combo, self.verify_user_combo,
                self.encrypt_user_combo, self.decrypt_user_combo,
                self.enc_sign_signer_combo, self.enc_sign_recipient_combo
            ]

            for combo in combo_boxes:
                combo['values'] = users
                if users and not combo.get():
                    combo.set(users[0])

        except Exception as e:
            pass  # Silently handle errors during refresh

    # Setup Tab Methods
    def initialize_ca(self):
        """Initialize Certificate Authority"""
        def init_ca():
            self.show_progress()
            self.update_status("Initializing CA...")
            self.clear_output(self.setup_output)

            self.append_output(self.setup_output, "🏗️ Initializing Certificate Authority...")
            result = self.safe_pki_operation(create_ca, self.setup_output)

            if result is not None:
                self.append_output(self.setup_output, "✅ CA initialized successfully!")
                self.check_pki_status()

        self.run_in_thread(init_ca)

    def check_ca_status(self):
        """Check CA status"""
        self.clear_output(self.setup_output)
        self.append_output(self.setup_output, "🔍 Checking CA Status...")

        ca_cert_exists = os.path.exists("ca_cert.pem")
        ca_key_exists = os.path.exists("ca_key.pem")

        self.append_output(self.setup_output, f"CA Certificate: {'✅ Found' if ca_cert_exists else '❌ Missing'}")
        self.append_output(self.setup_output, f"CA Private Key: {'✅ Found' if ca_key_exists else '❌ Missing'}")

        if ca_cert_exists and ca_key_exists:
            self.append_output(self.setup_output, "✅ CA is properly configured")
        else:
            self.append_output(self.setup_output, "❌ CA needs to be initialized")

    def list_pki_files(self):
        """List all PKI files"""
        def list_files():
            self.show_progress()
            self.update_status("Listing PKI files...")
            self.clear_output(self.setup_output)

            self.append_output(self.setup_output, "📁 PKI Files:")
            result = self.safe_pki_operation(list_pki_files, self.setup_output)

            if result:
                for file_type, files in result.items():
                    self.append_output(self.setup_output, f"\n{file_type.title()}:")
                    for file_path in files:
                        self.append_output(self.setup_output, f"  - {file_path.name}")

        self.run_in_thread(list_files)

    def open_pki_directory(self):
        """Open PKI directory in file explorer"""
        try:
            import subprocess
            import platform

            current_dir = os.getcwd()

            if platform.system() == "Windows":
                subprocess.run(["explorer", current_dir])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", current_dir])
            else:  # Linux
                subprocess.run(["xdg-open", current_dir])

        except Exception as e:
            messagebox.showerror("Error", f"Could not open directory: {e}")

    # Users Tab Methods
    def register_user(self):
        """Register a new user"""
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showwarning("Warning", "Please enter a username")
            return

        def reg_user():
            self.show_progress()
            self.update_status(f"Registering user: {username}")
            self.clear_output(self.users_output)

            self.append_output(self.users_output, f"👤 Registering user: {username}")
            result = self.safe_pki_operation(register_user, self.users_output, username)

            if result is not None:
                self.append_output(self.users_output, f"✅ User '{username}' registered successfully!")
                self.username_entry.delete(0, tk.END)
                self.refresh_users()

        self.run_in_thread(reg_user)

    def get_cert_info(self):
        """Get certificate information for selected user"""
        username = self.user_combo.get()
        if not username:
            messagebox.showwarning("Warning", "Please select a user")
            return

        def get_info():
            self.show_progress()
            self.update_status(f"Getting certificate info for: {username}")
            self.clear_output(self.users_output)

            cert_path = f"{username}_cert.pem"
            self.append_output(self.users_output, f"📜 Certificate Information for: {username}")

            result = self.safe_pki_operation(get_certificate_info, self.users_output, cert_path)

            if result:
                self.append_output(self.users_output, f"Subject: {result['subject']}")
                self.append_output(self.users_output, f"Issuer: {result['issuer']}")
                self.append_output(self.users_output, f"Serial Number: {result['serial_number']}")
                self.append_output(self.users_output, f"Valid From: {result['not_valid_before']}")
                self.append_output(self.users_output, f"Valid Until: {result['not_valid_after']}")
                self.append_output(self.users_output, f"Key Size: {result['public_key_size']} bits")

        self.run_in_thread(get_info)

    def get_key_info(self):
        """Get key information for selected user"""
        username = self.user_combo.get()
        if not username:
            messagebox.showwarning("Warning", "Please select a user")
            return

        def get_info():
            self.show_progress()
            self.update_status(f"Getting key info for: {username}")
            self.clear_output(self.users_output)

            key_path = f"{username}_key.pem"
            self.append_output(self.users_output, f"🔑 Key Information for: {username}")

            result = self.safe_pki_operation(get_key_info, self.users_output, key_path)

            if result:
                self.append_output(self.users_output, f"Key Type: {result['key_type']}")
                self.append_output(self.users_output, f"Key Size: {result['key_size']} bits")

        self.run_in_thread(get_info)

    # Document Tab Methods
    def browse_document(self):
        """Browse for document to sign"""
        filename = filedialog.askopenfilename(
            title="Select Document to Sign",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.doc_path_var.set(filename)

    def browse_verify_document(self):
        """Browse for document to verify"""
        filename = filedialog.askopenfilename(
            title="Select Document to Verify",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.verify_doc_var.set(filename)

    def sign_document(self):
        """Sign a document"""
        username = self.sign_user_combo.get()
        doc_path = self.doc_path_var.get()

        if not username:
            messagebox.showwarning("Warning", "Please select a signer")
            return
        if not doc_path:
            messagebox.showwarning("Warning", "Please select a document")
            return

        def sign_doc():
            self.show_progress()
            self.update_status(f"Signing document: {os.path.basename(doc_path)}")
            self.clear_output(self.docs_output)

            self.append_output(self.docs_output, f"✍️ Signing document: {os.path.basename(doc_path)}")
            self.append_output(self.docs_output, f"Signer: {username}")

            result = self.safe_pki_operation(sign_document, self.docs_output, username, doc_path)

            if result:
                self.append_output(self.docs_output, f"✅ Document signed successfully!")
                self.append_output(self.docs_output, f"Signature file: {doc_path}.sig")

        self.run_in_thread(sign_doc)

    def verify_document(self):
        """Verify a document signature"""
        username = self.verify_user_combo.get()
        doc_path = self.verify_doc_var.get()

        if not username:
            messagebox.showwarning("Warning", "Please select a signer")
            return
        if not doc_path:
            messagebox.showwarning("Warning", "Please select a document")
            return

        def verify_doc():
            self.show_progress()
            self.update_status(f"Verifying signature: {os.path.basename(doc_path)}")
            self.clear_output(self.docs_output)

            self.append_output(self.docs_output, f"🔍 Verifying signature for: {os.path.basename(doc_path)}")
            self.append_output(self.docs_output, f"Expected signer: {username}")

            result = self.safe_pki_operation(verify_signature, self.docs_output, username, doc_path)

            if result:
                self.append_output(self.docs_output, f"✅ Signature is valid!")

        self.run_in_thread(verify_doc)

    # Encryption Tab Methods
    def browse_encrypt_file(self):
        """Browse for file to encrypt"""
        filename = filedialog.askopenfilename(
            title="Select File to Encrypt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.encrypt_file_var.set(filename)

    def browse_decrypt_file(self):
        """Browse for file to decrypt"""
        filename = filedialog.askopenfilename(
            title="Select Encrypted File",
            filetypes=[("Encrypted files", "*.enc"), ("All files", "*.*")]
        )
        if filename:
            self.decrypt_file_var.set(filename)

    def browse_enc_sign_file(self):
        """Browse for file to encrypt and sign"""
        filename = filedialog.askopenfilename(
            title="Select File to Encrypt and Sign",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.enc_sign_file_var.set(filename)

    def encrypt_file(self):
        """Encrypt a file"""
        username = self.encrypt_user_combo.get()
        file_path = self.encrypt_file_var.get()

        if not username:
            messagebox.showwarning("Warning", "Please select a recipient")
            return
        if not file_path:
            messagebox.showwarning("Warning", "Please select a file")
            return

        def encrypt():
            self.show_progress()
            self.update_status(f"Encrypting file: {os.path.basename(file_path)}")
            self.clear_output(self.enc_output)

            self.append_output(self.enc_output, f"🔐 Encrypting file: {os.path.basename(file_path)}")
            self.append_output(self.enc_output, f"Recipient: {username}")

            result = self.safe_pki_operation(encrypt_document, self.enc_output, username, file_path)

            if result:
                self.append_output(self.enc_output, f"✅ File encrypted successfully!")
                self.append_output(self.enc_output, f"Encrypted file: {result}")

        self.run_in_thread(encrypt)

    def decrypt_file(self):
        """Decrypt a file"""
        username = self.decrypt_user_combo.get()
        file_path = self.decrypt_file_var.get()

        if not username:
            messagebox.showwarning("Warning", "Please select your username")
            return
        if not file_path:
            messagebox.showwarning("Warning", "Please select an encrypted file")
            return

        def decrypt():
            self.show_progress()
            self.update_status(f"Decrypting file: {os.path.basename(file_path)}")
            self.clear_output(self.enc_output)

            self.append_output(self.enc_output, f"🔓 Decrypting file: {os.path.basename(file_path)}")
            self.append_output(self.enc_output, f"User: {username}")

            result = self.safe_pki_operation(decrypt_document, self.enc_output, username, file_path)

            if result:
                self.append_output(self.enc_output, f"✅ File decrypted successfully!")
                self.append_output(self.enc_output, f"Decrypted file: {result}")

        self.run_in_thread(decrypt)

    def encrypt_and_sign(self):
        """Encrypt and sign a file"""
        signer = self.enc_sign_signer_combo.get()
        recipient = self.enc_sign_recipient_combo.get()
        file_path = self.enc_sign_file_var.get()

        if not signer:
            messagebox.showwarning("Warning", "Please select a signer")
            return
        if not recipient:
            messagebox.showwarning("Warning", "Please select a recipient")
            return
        if not file_path:
            messagebox.showwarning("Warning", "Please select a file")
            return

        def enc_sign():
            self.show_progress()
            self.update_status(f"Encrypting and signing: {os.path.basename(file_path)}")
            self.clear_output(self.enc_output)

            self.append_output(self.enc_output, f"🔐✍️ Encrypting and signing: {os.path.basename(file_path)}")
            self.append_output(self.enc_output, f"Signer: {signer}")
            self.append_output(self.enc_output, f"Recipient: {recipient}")

            result = self.safe_pki_operation(encrypt_and_sign_document, self.enc_output, signer, recipient, file_path)

            if result:
                encrypted_file, signature_file = result
                self.append_output(self.enc_output, f"✅ File encrypted and signed successfully!")
                self.append_output(self.enc_output, f"Encrypted file: {encrypted_file}")
                self.append_output(self.enc_output, f"Signature file: {signature_file}")

        self.run_in_thread(enc_sign)

    # Security Tab Methods
    def browse_test_signed(self):
        """Browse for signed document for testing"""
        filename = filedialog.askopenfilename(
            title="Select Signed Document",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.test_signed_var.set(filename)

    def browse_test_encrypted(self):
        """Browse for encrypted file for testing"""
        filename = filedialog.askopenfilename(
            title="Select Encrypted File",
            filetypes=[("Encrypted files", "*.enc"), ("All files", "*.*")]
        )
        if filename:
            self.test_encrypted_var.set(filename)

    def run_all_attacks(self):
        """Run all security attack simulations"""
        signed_doc = self.test_signed_var.get()
        encrypted_file = self.test_encrypted_var.get()

        if not signed_doc:
            messagebox.showwarning("Warning", "Please select a signed document")
            return
        if not encrypted_file:
            messagebox.showwarning("Warning", "Please select an encrypted file")
            return

        def run_attacks():
            self.show_progress()
            self.update_status("Running security attack simulations...")
            self.clear_output(self.sec_output)

            self.append_output(self.sec_output, "🛡️ Running All Security Attack Simulations")
            self.append_output(self.sec_output, "=" * 50)

            try:
                simulator = AttackSimulator()
                simulator.run_all_attacks(signed_doc, encrypted_file)

                self.append_output(self.sec_output, "\n✅ All attack simulations completed!")

            except Exception as e:
                self.append_output(self.sec_output, f"❌ Attack simulation failed: {e}")

        self.run_in_thread(run_attacks)

    def test_document_tampering(self):
        """Test document tampering attack"""
        signed_doc = self.test_signed_var.get()

        if not signed_doc:
            messagebox.showwarning("Warning", "Please select a signed document")
            return

        def test_tampering():
            self.show_progress()
            self.update_status("Testing document tampering...")
            self.clear_output(self.sec_output)

            self.append_output(self.sec_output, "🛡️ Testing Document Tampering Attack")
            self.append_output(self.sec_output, "-" * 40)

            try:
                simulator = AttackSimulator()
                simulator.document_tampering_attack(signed_doc)

                self.append_output(self.sec_output, "\n✅ Document tampering test completed!")

            except Exception as e:
                self.append_output(self.sec_output, f"❌ Test failed: {e}")

        self.run_in_thread(test_tampering)

    def test_signature_spoofing(self):
        """Test signature spoofing attack"""
        signed_doc = self.test_signed_var.get()

        if not signed_doc:
            messagebox.showwarning("Warning", "Please select a signed document")
            return

        def test_spoofing():
            self.show_progress()
            self.update_status("Testing signature spoofing...")
            self.clear_output(self.sec_output)

            self.append_output(self.sec_output, "🛡️ Testing Signature Spoofing Attack")
            self.append_output(self.sec_output, "-" * 40)

            try:
                simulator = AttackSimulator()
                # Use first available user for testing
                users = [f.replace("_cert.pem", "") for f in os.listdir(".") if f.endswith("_cert.pem") and not f.startswith("ca_")]
                if users:
                    simulator.signature_spoofing_attack(users[0], signed_doc)
                    self.append_output(self.sec_output, "\n✅ Signature spoofing test completed!")
                else:
                    self.append_output(self.sec_output, "❌ No users found for testing")

            except Exception as e:
                self.append_output(self.sec_output, f"❌ Test failed: {e}")

        self.run_in_thread(test_spoofing)

    # Reports Tab Methods
    def generate_system_report(self):
        """Generate comprehensive system report"""
        def gen_report():
            self.show_progress()
            self.update_status("Generating system report...")
            self.clear_output(self.reports_output)

            self.append_output(self.reports_output, "📊 Generating System Report...")
            self.append_output(self.reports_output, "=" * 50)

            try:
                report = generate_system_report()

                if report:
                    # Display report content
                    self.append_output(self.reports_output, f"Generated: {report['timestamp']}")
                    self.append_output(self.reports_output, "")

                    # PKI Setup Status
                    setup = report['pki_setup']
                    self.append_output(self.reports_output, "PKI SETUP STATUS:")
                    self.append_output(self.reports_output, f"  CA Certificate: {'OK' if setup['ca_cert'] else 'MISSING'}")
                    self.append_output(self.reports_output, f"  CA Private Key: {'OK' if setup['ca_key'] else 'MISSING'}")
                    self.append_output(self.reports_output, f"  Registered Users: {len(setup['users'])}")

                    for user in setup['users']:
                        cert_status = 'OK' if user['certificate'] else 'MISSING'
                        key_status = 'OK' if user['private_key'] else 'MISSING'
                        self.append_output(self.reports_output, f"    - {user['username']}: Cert {cert_status}, Key {key_status}")

                    # File Summary
                    self.append_output(self.reports_output, "\nFILE SUMMARY:")
                    files = report['files']
                    for file_type, file_list in files.items():
                        self.append_output(self.reports_output, f"  {file_type.title()}: {len(file_list)}")

                    # Save report to file
                    filename = "signatrix_report.txt"
                    if save_report_to_file(report, filename):
                        self.append_output(self.reports_output, f"\n✅ Report saved to: {filename}")

                else:
                    self.append_output(self.reports_output, "❌ Failed to generate report")

            except Exception as e:
                self.append_output(self.reports_output, f"❌ Report generation failed: {e}")

        self.run_in_thread(gen_report)

    def detailed_pki_status(self):
        """Show detailed PKI status"""
        def check_status():
            self.show_progress()
            self.update_status("Checking PKI status...")
            self.clear_output(self.reports_output)

            self.append_output(self.reports_output, "🔍 Detailed PKI Status Check")
            self.append_output(self.reports_output, "=" * 40)

            try:
                status = verify_pki_setup()

                if status:
                    self.append_output(self.reports_output, "PKI CONFIGURATION:")
                    self.append_output(self.reports_output, f"  CA Certificate: {'✅ Found' if status['ca_cert'] else '❌ Missing'}")
                    self.append_output(self.reports_output, f"  CA Private Key: {'✅ Found' if status['ca_key'] else '❌ Missing'}")
                    self.append_output(self.reports_output, f"  Total Users: {len(status['users'])}")

                    if status['users']:
                        self.append_output(self.reports_output, "\nREGISTERED USERS:")
                        for user in status['users']:
                            cert_icon = '✅' if user['certificate'] else '❌'
                            key_icon = '✅' if user['private_key'] else '❌'
                            self.append_output(self.reports_output, f"  {user['username']}: {cert_icon} Cert, {key_icon} Key")

                    self.append_output(self.reports_output, "\n✅ PKI status check completed!")
                else:
                    self.append_output(self.reports_output, "❌ Failed to check PKI status")

            except Exception as e:
                self.append_output(self.reports_output, f"❌ Status check failed: {e}")

        self.run_in_thread(check_status)


def main():
    """Main application entry point"""
    # Create main window
    root = tk.Tk()

    # Create and run Signatrix application
    app = SignatrixGUI(root)

    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    # Start the GUI event loop
    root.mainloop()


if __name__ == "__main__":
    print("🔐 Starting Signatrix - PKI Document Signing System")
    print("=" * 50)
    main()
