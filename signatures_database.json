{"alice_4ad0788bce9ac8417146ea7a1dc77dcb18434cfd51741fa96d654bf7035fabe4_1752482050.267344": {"document_path": "business_contract.txt", "document_hash": "4ad0788bce9ac8417146ea7a1dc77dcb18434cfd51741fa96d654bf7035fabe4", "signer_username": "alice", "signer_certificate_serial": "709499040690409549166022921144251472362396038097", "signing_time": "2025-07-14T14:19:10.267344", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 561}, "bob_ba86c99520c5c08b6da45e2ce59f8221e42cae5fae9a125ce96cee61504c2208_1752482050.367187": {"document_path": "nda_agreement.txt", "document_hash": "ba86c99520c5c08b6da45e2ce59f8221e42cae5fae9a125ce96cee61504c2208", "signer_username": "bob", "signer_certificate_serial": "313511140206795937730827369494025148101455826800", "signing_time": "2025-07-14T14:19:10.367187", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 512}, "charlie_a3c092a435d15c8d1b76d94e68e56dded05284e9bc1730ad2541743b3cb78a99_1752482050.467339": {"document_path": "employment_offer.txt", "document_hash": "a3c092a435d15c8d1b76d94e68e56dded05284e9bc1730ad2541743b3cb78a99", "signer_username": "charlie", "signer_certificate_serial": "404948351264679685695302743436469268537164840322", "signing_time": "2025-07-14T14:19:10.467339", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 585}, "eve_639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07_1752482053.217518": {"document_path": "legal_doc_65cdca2376c24b1e.txt", "document_hash": "639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07", "signer_username": "eve", "signer_certificate_serial": "10247317946639456654818809356039521113784132128", "signing_time": "2025-07-14T14:19:13.208966", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 1180}, "charlie_639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07_1752482053.3405": {"document_path": "legal_doc_65cdca2376c24b1e.txt", "document_hash": "639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07", "signer_username": "charlie", "signer_certificate_serial": "404948351264679685695302743436469268537164840322", "signing_time": "2025-07-14T14:19:13.340500", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 1180}, "alice_639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07_1752482053.472323": {"document_path": "legal_doc_65cdca2376c24b1e.txt", "document_hash": "639f8344dc77d1139757f294143c5b07c74d79d9c32977dbf1a4e6376c057e07", "signer_username": "alice", "signer_certificate_serial": "709499040690409549166022921144251472362396038097", "signing_time": "2025-07-14T14:19:13.472323", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 1180}, "alice_441cfd6d0a53ae910716f23e8cc54a08869a100fbc8b40bf15c9daa603ddec42_1752482453.943848": {"document_path": "quick_test.txt", "document_hash": "441cfd6d0a53ae910716f23e8cc54a08869a100fbc8b40bf15c9daa603ddec42", "signer_username": "alice", "signer_certificate_serial": "709499040690409549166022921144251472362396038097", "signing_time": "2025-07-14T14:25:53.943848", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 30}}