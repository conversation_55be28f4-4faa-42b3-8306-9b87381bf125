{"alice_f8f9ce67a4af1dd54f156cdeedb8374302f8dbefc86bf85a06550a02cff6b636_1752480185.11525": {"document_path": "test_contract.txt", "document_hash": "f8f9ce67a4af1dd54f156cdeedb8374302f8dbefc86bf85a06550a02cff6b636", "signer_username": "alice", "signer_certificate_serial": "322667699669112624708082577414922957264807795154", "signing_time": "2025-07-14T13:48:05.112569", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 116}, "bob_f8f9ce67a4af1dd54f156cdeedb8374302f8dbefc86bf85a06550a02cff6b636_1752480185.19789": {"document_path": "test_contract.txt", "document_hash": "f8f9ce67a4af1dd54f156cdeedb8374302f8dbefc86bf85a06550a02cff6b636", "signer_username": "bob", "signer_certificate_serial": "192115084429026872818585090345655735295997610895", "signing_time": "2025-07-14T13:48:05.193940", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 116}, "alice_dfcca60ddea28a4b8610147b3c1a8e38884b82d6e0ad8231e6b6a499b684bcae_1752480185.307913": {"document_path": "integrity_test.txt", "document_hash": "dfcca60ddea28a4b8610147b3c1a8e38884b82d6e0ad8231e6b6a499b684bcae", "signer_username": "alice", "signer_certificate_serial": "322667699669112624708082577414922957264807795154", "signing_time": "2025-07-14T13:48:05.306544", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 48}, "alice_bab85b03c8e9b8415fda464726215f0ebd80bacaca3075c24ac0d3b8e92525e8_1752480185.697784": {"document_path": "attack_test_document.txt", "document_hash": "bab85b03c8e9b8415fda464726215f0ebd80bacaca3075c24ac0d3b8e92525e8", "signer_username": "alice", "signer_certificate_serial": "322667699669112624708082577414922957264807795154", "signing_time": "2025-07-14T13:48:05.691605", "signature_algorithm": "RSA-PSS with SHA-256", "key_size": 2048, "document_size": 57}}