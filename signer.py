import hashlib
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from error_handler import (
    SignatureError, KeyError, ValidationError,
    handle_pki_errors, validate_file_path, validate_username,
    PKIErrorContext
)

@handle_pki_errors(SignatureError)
def sign_document(username, doc_path):
    """
    Sign a document using the user's private key

    Args:
        username: Username of the signer
        doc_path: Path to the document to sign

    Returns:
        str: Path to the signature file

    Raises:
        SignatureError: If signing fails
        ValidationError: If input validation fails
    """
    with PKIErrorContext("Document Signing", SignatureError):
        # Validate inputs
        validate_username(username)
        validate_file_path(doc_path)

        key_path = f"{username}_key.pem"
        validate_file_path(key_path)

        # Load private key
        try:
            with open(key_path, "rb") as f:
                private_key = serialization.load_pem_private_key(f.read(), password=None)
        except Exception as e:
            raise KeyError(
                f"Failed to load private key for user {username}",
                error_code="KEY_LOAD_FAILED",
                details={'key_path': key_path, 'error': str(e)}
            )

        # Read document
        try:
            with open(doc_path, "rb") as f:
                data = f.read()
        except Exception as e:
            raise SignatureError(
                f"Failed to read document for signing: {doc_path}",
                error_code="DOC_READ_FAILED",
                details={'document_path': doc_path, 'error': str(e)}
            )

        # Create signature
        try:
            signature = private_key.sign(
                data,
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
        except Exception as e:
            raise SignatureError(
                f"Failed to create signature for document: {doc_path}",
                error_code="SIG_CREATE_FAILED",
                details={'document_path': doc_path, 'username': username, 'error': str(e)}
            )

        # Save signature
        sig_path = f"{doc_path}.sig"
        try:
            with open(sig_path, "wb") as f:
                f.write(signature)
        except Exception as e:
            raise SignatureError(
                f"Failed to save signature file: {sig_path}",
                error_code="SIG_SAVE_FAILED",
                details={'signature_path': sig_path, 'error': str(e)}
            )

        print(f"[✓] Document '{doc_path}' signed by {username}.")
        return sig_path
