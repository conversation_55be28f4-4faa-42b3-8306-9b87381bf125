import hashlib
import os
import json
from datetime import datetime
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509
from error_handler import (
    SignatureError, KeyError, ValidationError,
    handle_pki_errors, validate_file_path, validate_username,
    PKIErrorContext
)
from user import authenticate_user

class DocumentSigningSystem:
    """Enhanced document signing system with integrity checks and audit trail"""

    def __init__(self):
        self.signatures_db_file = "signatures_database.json"
        self.load_signatures_database()

    def load_signatures_database(self):
        """Load signatures database from file"""
        try:
            if os.path.exists(self.signatures_db_file):
                with open(self.signatures_db_file, 'r') as f:
                    self.signatures_db = json.load(f)
            else:
                self.signatures_db = {}
        except Exception:
            self.signatures_db = {}

    def save_signatures_database(self):
        """Save signatures database to file"""
        try:
            with open(self.signatures_db_file, 'w') as f:
                json.dump(self.signatures_db, f, indent=2, default=str)
        except Exception as e:
            raise SignatureError(f"Failed to save signatures database: {e}")

    def calculate_document_hash(self, doc_path):
        """Calculate SHA-256 hash of document for integrity verification"""
        try:
            with open(doc_path, "rb") as f:
                data = f.read()
            return hashlib.sha256(data).hexdigest()
        except Exception as e:
            raise SignatureError(f"Failed to calculate document hash: {e}")

    @handle_pki_errors(SignatureError)
    def sign_document(self, username, doc_path, require_authentication=True):
        """
        Sign a document using the user's private key with enhanced security

        Args:
            username: Username of the signer
            doc_path: Path to the document to sign
            require_authentication: Whether to require user authentication

        Returns:
            dict: Signing result with metadata

        Raises:
            SignatureError: If signing fails
            ValidationError: If input validation fails
        """
        with PKIErrorContext("Document Signing", SignatureError):
            # Validate inputs
            validate_username(username)
            validate_file_path(doc_path)

            # Authenticate user if required
            if require_authentication:
                challenge_data = f"sign_document_{username}_{datetime.utcnow().isoformat()}"
                auth_result = authenticate_user(username, challenge_data)
                if not auth_result['authenticated']:
                    raise ValidationError(f"User authentication failed for {username}")

            key_path = f"{username}_key.pem"
            cert_path = f"{username}_cert.pem"
            validate_file_path(key_path)
            validate_file_path(cert_path)

            # Calculate document hash for integrity
            doc_hash = self.calculate_document_hash(doc_path)

            # Load private key
            try:
                with open(key_path, "rb") as f:
                    private_key = serialization.load_pem_private_key(f.read(), password=None)
            except Exception as e:
                raise KeyError(
                    f"Failed to load private key for user {username}",
                    error_code="KEY_LOAD_FAILED",
                    details={'key_path': key_path, 'error': str(e)}
                )

            # Load certificate for metadata
            try:
                with open(cert_path, "rb") as f:
                    cert = x509.load_pem_x509_certificate(f.read())
            except Exception as e:
                raise SignatureError(f"Failed to load certificate: {e}")

            # Read document
            try:
                with open(doc_path, "rb") as f:
                    data = f.read()
            except Exception as e:
                raise SignatureError(
                    f"Failed to read document for signing: {doc_path}",
                    error_code="DOC_READ_FAILED",
                    details={'document_path': doc_path, 'error': str(e)}
                )

            # Create signature
            try:
                signature = private_key.sign(
                    data,
                    padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                    hashes.SHA256()
                )
            except Exception as e:
                raise SignatureError(
                    f"Failed to create signature for document: {doc_path}",
                    error_code="SIG_CREATE_FAILED",
                    details={'document_path': doc_path, 'username': username, 'error': str(e)}
                )

            # Create signature metadata
            signature_metadata = {
                'document_path': doc_path,
                'document_hash': doc_hash,
                'signer_username': username,
                'signer_certificate_serial': str(cert.serial_number),
                'signing_time': datetime.utcnow().isoformat(),
                'signature_algorithm': 'RSA-PSS with SHA-256',
                'key_size': private_key.key_size,
                'document_size': len(data)
            }

            # Save signature with metadata
            sig_path = f"{doc_path}.sig"
            metadata_path = f"{doc_path}.sig.meta"

            try:
                # Save binary signature
                with open(sig_path, "wb") as f:
                    f.write(signature)

                # Save metadata
                with open(metadata_path, "w") as f:
                    json.dump(signature_metadata, f, indent=2)

            except Exception as e:
                raise SignatureError(
                    f"Failed to save signature files: {sig_path}",
                    error_code="SIG_SAVE_FAILED",
                    details={'signature_path': sig_path, 'error': str(e)}
                )

            # Store in signatures database
            signature_id = f"{username}_{doc_hash}_{datetime.utcnow().timestamp()}"
            self.signatures_db[signature_id] = signature_metadata
            self.save_signatures_database()

            result = {
                'signature_id': signature_id,
                'signature_path': sig_path,
                'metadata_path': metadata_path,
                'document_hash': doc_hash,
                'signing_time': signature_metadata['signing_time']
            }

            print(f"[✓] Document '{doc_path}' signed by {username} with ID: {signature_id}")
            return result


# Global signing system instance
signing_system = DocumentSigningSystem()

# Backward compatibility function
@handle_pki_errors(SignatureError)
def sign_document(username, doc_path):
    """Sign a document (backward compatible)"""
    result = signing_system.sign_document(username, doc_path, require_authentication=False)
    return result['signature_path']
