from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature
from encryptor import encrypt_document, decrypt_document, encrypt_and_sign_document, verify_and_decrypt_document
from attack_simulator import AttackSimulator
from utils import get_certificate_info, get_key_info, verify_pki_setup, generate_system_report, save_report_to_file
from error_handler import PKIError, SignatureError, CertificateError, ValidationError

def test_basic_pki():
    """Test basic PKI functionality"""
    print("\n=== Testing Basic PKI Functionality ===")
    create_ca()
    register_user("alice")
    register_user("bob")

    with open("contract.txt", "w") as f:
        f.write("This is a legal contract between <PERSON> and <PERSON>.")

    sign_document("alice", "contract.txt")
    verify_signature("alice", "contract.txt")

    # This should fail - Bob trying to verify Alice's signature
    try:
        verify_signature("bob", "contract.txt")
        print("[✗] SECURITY BREACH: <PERSON> was able to verify <PERSON>'s signature!")
    except Exception:
        print("[✓] Security working: <PERSON> correctly cannot verify <PERSON>'s signature")

def test_hybrid_encryption():
    """Test hybrid encryption functionality"""
    print("\n=== Testing Hybrid Encryption ===")

    # Create a confidential document
    with open("confidential.txt", "w") as f:
        f.write("This is a confidential document containing sensitive information.\nOnly authorized users should be able to read this.")

    # Test basic encryption/decryption
    print("\n--- Basic Encryption/Decryption ---")
    encrypt_document("alice", "confidential.txt")
    decrypt_document("alice", "confidential.txt.enc")

    # Test encrypt and sign workflow
    print("\n--- Encrypt and Sign Workflow ---")
    with open("secret_contract.txt", "w") as f:
        f.write("SECRET CONTRACT: This document contains confidential terms between Alice and Bob.")

    # Alice encrypts for Bob and signs it
    encrypted_path, signature_path = encrypt_and_sign_document("alice", "bob", "secret_contract.txt")

    if encrypted_path and signature_path:
        # Bob verifies and decrypts
        decrypted_path = verify_and_decrypt_document("alice", "bob", encrypted_path)

        if decrypted_path:
            print(f"[✓] Successfully processed secure document workflow")
            # Show the decrypted content
            with open(decrypted_path, "r") as f:
                print(f"[INFO] Decrypted content: {f.read()}")

def test_attack_simulation():
    """Test attack simulation functionality"""
    print("\n=== Testing Attack Simulation ===")

    # Create attack simulator
    simulator = AttackSimulator()

    # Run all attacks on our test files
    # We need a signed document and an encrypted file
    signed_doc = "contract.txt"  # This should exist from basic PKI test
    encrypted_file = "secret_contract.txt.enc"  # This should exist from encryption test

    simulator.run_all_attacks(signed_doc, encrypted_file)

def test_utils():
    """Test utility functions"""
    print("\n=== Testing Utility Functions ===")

    # Test certificate info extraction
    print("\n--- Certificate Information ---")
    alice_cert_info = get_certificate_info("alice_cert.pem")
    if alice_cert_info:
        print(f"[✓] Alice's certificate info extracted")
        print(f"    Subject: {alice_cert_info['subject']}")
        print(f"    Key Size: {alice_cert_info['public_key_size']} bits")
        print(f"    Valid Until: {alice_cert_info['not_valid_after']}")

    # Test key info extraction
    print("\n--- Private Key Information ---")
    alice_key_info = get_key_info("alice_key.pem")
    if alice_key_info:
        print(f"[✓] Alice's key info extracted")
        print(f"    Key Type: {alice_key_info['key_type']}")
        print(f"    Key Size: {alice_key_info['key_size']} bits")

    # Test PKI setup verification
    print("\n--- PKI Setup Verification ---")
    pki_status = verify_pki_setup()
    print(f"[✓] PKI setup verified - {len(pki_status['users'])} users registered")

    # Generate and save system report
    print("\n--- System Report Generation ---")
    report = generate_system_report()
    if save_report_to_file(report):
        print("[✓] System report generated and saved")

def test_error_handling():
    """Test error handling functionality"""
    print("\n=== Testing Error Handling ===")

    # Test validation errors
    print("\n--- Validation Error Tests ---")
    try:
        sign_document("", "nonexistent.txt")
        print("[✗] Should have failed with empty username")
    except ValidationError as e:
        print(f"[✓] Correctly caught validation error: {e}")
    except Exception as e:
        print(f"[✓] Caught error (expected): {type(e).__name__}: {e}")

    # Test file not found errors
    print("\n--- File Not Found Error Tests ---")
    try:
        sign_document("nonexistent_user", "nonexistent.txt")
        print("[✗] Should have failed with missing files")
    except (PKIError, Exception) as e:
        print(f"[✓] Correctly caught file error: {type(e).__name__}: {e}")

    # Test signature verification with missing files
    print("\n--- Signature Verification Error Tests ---")
    try:
        verify_signature("nonexistent_user", "nonexistent.txt")
        print("[✗] Should have failed with missing files")
    except (PKIError, Exception) as e:
        print(f"[✓] Correctly caught verification error: {type(e).__name__}: {e}")

def run():
    test_basic_pki()
    test_hybrid_encryption()
    test_attack_simulation()
    test_utils()
    test_error_handling()

if __name__ == "__main__":
    run()
