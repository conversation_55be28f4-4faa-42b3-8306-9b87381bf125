from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature

def run():
    create_ca()
    register_user("alice")
    register_user("bob")

    with open("contract.txt", "w") as f:
        f.write("This is a legal contract between <PERSON> and <PERSON>.")

    sign_document("alice", "contract.txt")
    verify_signature("alice", "contract.txt")
    verify_signature("bob", "contract.txt")  # should fail

if __name__ == "__main__":
    run()
