#!/usr/bin/env python3
"""
Comprehensive Test Suite for PKI Document Signing System
Includes unit tests, integration tests, and security tests
"""

import unittest
import os
import tempfile
import shutil
from pathlib import Path

# Import PKI modules
from ca import create_ca
from user import register_user
from signer import sign_document
from verifier import verify_signature
from encryptor import (
    encrypt_document, decrypt_document,
    encrypt_and_sign_document, verify_and_decrypt_document
)
from attack_simulator import AttackSimulator
from utils import (
    get_certificate_info, get_key_info, verify_pki_setup,
    generate_system_report, save_report_to_file
)
from error_handler import PKIError, SignatureError, CertificateError, ValidationError


class TestPKISetup(unittest.TestCase):
    """Test PKI setup and initialization"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_ca_creation(self):
        """Test Certificate Authority creation"""
        create_ca()
        
        # Check if CA files are created
        self.assertTrue(os.path.exists("ca_cert.pem"))
        self.assertTrue(os.path.exists("ca_key.pem"))
        
        # Check file sizes (should not be empty)
        self.assertGreater(os.path.getsize("ca_cert.pem"), 0)
        self.assertGreater(os.path.getsize("ca_key.pem"), 0)
    
    def test_user_registration(self):
        """Test user registration"""
        create_ca()
        register_user("testuser")
        
        # Check if user files are created
        self.assertTrue(os.path.exists("testuser_cert.pem"))
        self.assertTrue(os.path.exists("testuser_key.pem"))
        
        # Check file sizes
        self.assertGreater(os.path.getsize("testuser_cert.pem"), 0)
        self.assertGreater(os.path.getsize("testuser_key.pem"), 0)
    
    def test_multiple_users(self):
        """Test registration of multiple users"""
        create_ca()
        users = ["alice", "bob", "charlie"]
        
        for user in users:
            register_user(user)
            self.assertTrue(os.path.exists(f"{user}_cert.pem"))
            self.assertTrue(os.path.exists(f"{user}_key.pem"))


class TestDocumentSigning(unittest.TestCase):
    """Test document signing and verification"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
        
        # Set up PKI
        create_ca()
        register_user("alice")
        register_user("bob")
        
        # Create test document
        self.test_doc = "test_document.txt"
        with open(self.test_doc, "w") as f:
            f.write("This is a test document for signing.")
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_document_signing(self):
        """Test document signing"""
        sign_document("alice", self.test_doc)
        
        # Check if signature file is created
        sig_file = f"{self.test_doc}.sig"
        self.assertTrue(os.path.exists(sig_file))
        self.assertGreater(os.path.getsize(sig_file), 0)
    
    def test_signature_verification(self):
        """Test signature verification"""
        sign_document("alice", self.test_doc)
        
        # Verify signature
        result = verify_signature("alice", self.test_doc)
        self.assertTrue(result)
    
    def test_invalid_signature_verification(self):
        """Test verification with wrong user"""
        sign_document("alice", self.test_doc)
        
        # Bob should not be able to verify Alice's signature
        with self.assertRaises(SignatureError):
            verify_signature("bob", self.test_doc)
    
    def test_tampered_document(self):
        """Test verification of tampered document"""
        sign_document("alice", self.test_doc)
        
        # Tamper with the document
        with open(self.test_doc, "w") as f:
            f.write("This is a TAMPERED test document.")
        
        # Verification should fail
        with self.assertRaises(SignatureError):
            verify_signature("alice", self.test_doc)


class TestEncryption(unittest.TestCase):
    """Test encryption and decryption functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
        
        # Set up PKI
        create_ca()
        register_user("alice")
        register_user("bob")
        
        # Create test document
        self.test_doc = "secret_document.txt"
        self.test_content = "This is a secret document that should be encrypted."
        with open(self.test_doc, "w") as f:
            f.write(self.test_content)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_document_encryption(self):
        """Test document encryption"""
        encrypted_file = encrypt_document("alice", self.test_doc)
        
        # Check if encrypted file is created
        self.assertIsNotNone(encrypted_file)
        self.assertTrue(os.path.exists(encrypted_file))
        self.assertGreater(os.path.getsize(encrypted_file), 0)
    
    def test_document_decryption(self):
        """Test document decryption"""
        encrypted_file = encrypt_document("alice", self.test_doc)
        decrypted_file = decrypt_document("alice", encrypted_file)
        
        # Check if decrypted file is created
        self.assertIsNotNone(decrypted_file)
        self.assertTrue(os.path.exists(decrypted_file))
        
        # Check if content matches original
        with open(decrypted_file, "r") as f:
            decrypted_content = f.read()
        self.assertEqual(decrypted_content, self.test_content)
    
    def test_encrypt_and_sign_workflow(self):
        """Test encrypt and sign workflow"""
        encrypted_file, signature_file = encrypt_and_sign_document("alice", "bob", self.test_doc)
        
        # Check if both files are created
        self.assertIsNotNone(encrypted_file)
        self.assertIsNotNone(signature_file)
        self.assertTrue(os.path.exists(encrypted_file))
        self.assertTrue(os.path.exists(signature_file))
    
    def test_verify_and_decrypt_workflow(self):
        """Test verify and decrypt workflow"""
        encrypted_file, _ = encrypt_and_sign_document("alice", "bob", self.test_doc)
        decrypted_file = verify_and_decrypt_document("alice", "bob", encrypted_file)
        
        # Check if decryption was successful
        self.assertIsNotNone(decrypted_file)
        self.assertTrue(os.path.exists(decrypted_file))
        
        # Check content
        with open(decrypted_file, "r") as f:
            decrypted_content = f.read()
        self.assertEqual(decrypted_content, self.test_content)


class TestErrorHandling(unittest.TestCase):
    """Test error handling functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_invalid_username_validation(self):
        """Test validation of invalid usernames"""
        create_ca()
        
        # Test empty username
        with self.assertRaises(PKIError):
            sign_document("", "nonexistent.txt")
        
        # Test invalid characters
        with self.assertRaises(PKIError):
            sign_document("user@domain", "nonexistent.txt")
    
    def test_missing_files(self):
        """Test handling of missing files"""
        create_ca()
        register_user("alice")
        
        # Test signing non-existent document
        with self.assertRaises(PKIError):
            sign_document("alice", "nonexistent.txt")
        
        # Test verifying non-existent signature
        with open("test.txt", "w") as f:
            f.write("test")
        
        with self.assertRaises(PKIError):
            verify_signature("alice", "test.txt")
    
    def test_missing_user_files(self):
        """Test handling of missing user files"""
        create_ca()
        
        with open("test.txt", "w") as f:
            f.write("test")
        
        # Test with non-existent user
        with self.assertRaises(PKIError):
            sign_document("nonexistent", "test.txt")


class TestSecurityFeatures(unittest.TestCase):
    """Test security features and attack resistance"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
        
        # Set up PKI
        create_ca()
        register_user("alice")
        register_user("bob")
        
        # Create and sign test document
        self.test_doc = "contract.txt"
        with open(self.test_doc, "w") as f:
            f.write("This is a legal contract between Alice and Bob.")
        sign_document("alice", self.test_doc)
        
        # Create encrypted file
        self.encrypted_file = encrypt_document("bob", self.test_doc)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_attack_simulator_initialization(self):
        """Test attack simulator initialization"""
        simulator = AttackSimulator()
        self.assertIsInstance(simulator, AttackSimulator)
        self.assertEqual(len(simulator.attack_results), 0)
    
    def test_document_tampering_detection(self):
        """Test document tampering detection"""
        simulator = AttackSimulator()
        simulator.document_tampering_attack(self.test_doc)
        
        # Should have one attack result
        self.assertEqual(len(simulator.attack_results), 1)
        
        # Attack should fail (be detected)
        self.assertFalse(simulator.attack_results[0]['success'])
    
    def test_signature_spoofing_detection(self):
        """Test signature spoofing detection"""
        simulator = AttackSimulator()
        simulator.signature_spoofing_attack("alice", self.test_doc)
        
        # Should have one attack result
        self.assertEqual(len(simulator.attack_results), 1)
        
        # Attack should fail (be detected)
        self.assertFalse(simulator.attack_results[0]['success'])
    
    def test_certificate_spoofing_detection(self):
        """Test certificate spoofing detection"""
        simulator = AttackSimulator()
        simulator.certificate_spoofing_attack("alice")
        
        # Should have one attack result
        self.assertEqual(len(simulator.attack_results), 1)
        
        # Attack should fail (be detected)
        self.assertFalse(simulator.attack_results[0]['success'])
    
    def test_encryption_attack_resistance(self):
        """Test encryption attack resistance"""
        simulator = AttackSimulator()
        simulator.encryption_attack(self.encrypted_file)
        
        # Should have one attack result
        self.assertEqual(len(simulator.attack_results), 1)
        
        # Attack should fail (encryption should hold)
        self.assertFalse(simulator.attack_results[0]['success'])


class TestUtilities(unittest.TestCase):
    """Test utility functions"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_dir = os.getcwd()
        os.chdir(self.test_dir)
        
        # Set up PKI
        create_ca()
        register_user("alice")
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_dir)
        shutil.rmtree(self.test_dir)
    
    def test_certificate_info_extraction(self):
        """Test certificate information extraction"""
        cert_info = get_certificate_info("alice_cert.pem")
        
        self.assertIsNotNone(cert_info)
        self.assertIn('subject', cert_info)
        self.assertIn('issuer', cert_info)
        self.assertIn('serial_number', cert_info)
        self.assertIn('public_key_size', cert_info)
    
    def test_key_info_extraction(self):
        """Test key information extraction"""
        key_info = get_key_info("alice_key.pem")
        
        self.assertIsNotNone(key_info)
        self.assertIn('key_type', key_info)
        self.assertIn('key_size', key_info)
        self.assertEqual(key_info['key_size'], 2048)
    
    def test_pki_setup_verification(self):
        """Test PKI setup verification"""
        status = verify_pki_setup()
        
        self.assertIsNotNone(status)
        self.assertTrue(status['ca_cert'])
        self.assertTrue(status['ca_key'])
        self.assertEqual(len(status['users']), 1)
        self.assertEqual(status['users'][0]['username'], 'alice')
    
    def test_system_report_generation(self):
        """Test system report generation"""
        report = generate_system_report()
        
        self.assertIsNotNone(report)
        self.assertIn('timestamp', report)
        self.assertIn('pki_setup', report)
        self.assertIn('files', report)
        self.assertIn('certificates', report)
        self.assertIn('keys', report)
    
    def test_report_file_saving(self):
        """Test saving report to file"""
        report = generate_system_report()
        filename = "test_report.txt"
        
        result = save_report_to_file(report, filename)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(filename))
        self.assertGreater(os.path.getsize(filename), 0)


def run_all_tests():
    """Run all test suites"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestPKISetup,
        TestDocumentSigning,
        TestEncryption,
        TestErrorHandling,
        TestSecurityFeatures,
        TestUtilities
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == "__main__":
    print("="*60)
    print("PKI DOCUMENT SIGNING SYSTEM - COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    result = run_all_tests()
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n🎉 ALL TESTS PASSED! 🎉")
    else:
        print(f"\n❌ {len(result.failures + result.errors)} TEST(S) FAILED")
    
    print("="*60)
