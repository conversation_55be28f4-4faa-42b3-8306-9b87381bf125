import os
from cryptography import x509
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from datetime import datetime, timedelta
from cryptography.x509.oid import NameOID
from cryptography.hazmat.backends import default_backend

def register_user(username):
    key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
    with open(f"{username}_key.pem", "wb") as f:
        f.write(key.private_bytes(
            serialization.Encoding.PEM,
            serialization.PrivateFormat.TraditionalOpenSSL,
            serialization.NoEncryption()
        ))

    subject = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "NP"),
        x509.NameAttribute(NameOID.COMMON_NAME, username)
    ])
    csr = x509.CertificateSigningRequestBuilder().subject_name(subject).sign(key, hashes.SHA256())

    with open("ca_cert.pem", "rb") as f:
        ca_cert = x509.load_pem_x509_certificate(f.read(), default_backend())
    with open("ca_key.pem", "rb") as f:
        ca_key = serialization.load_pem_private_key(f.read(), password=None, backend=default_backend())

    cert = x509.CertificateBuilder().subject_name(csr.subject).issuer_name(
        ca_cert.subject).public_key(csr.public_key()).serial_number(
        x509.random_serial_number()).not_valid_before(
        datetime.utcnow()).not_valid_after(datetime.utcnow() + timedelta(days=365)
    ).sign(ca_key, hashes.SHA256())

    with open(f"{username}_cert.pem", "wb") as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))

    print(f"[✓] User '{username}' registered with certificate.")
