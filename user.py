from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
from cryptography import x509
from cryptography.x509.oid import NameOID
from datetime import datetime, timedelta, timezone
import os
import secrets
import json
from error_handler import P<PERSON><PERSON>rror, ValidationError, CertificateError


class UserAuthenticationSystem:
    """Enhanced user authentication system with certificate-based identity verification"""

    def __init__(self):
        self.users_db_file = "users_database.json"
        self.load_users_database()

    def load_users_database(self):
        """Load users database from file"""
        try:
            if os.path.exists(self.users_db_file):
                with open(self.users_db_file, 'r') as f:
                    self.users_db = json.load(f)
            else:
                self.users_db = {}
        except Exception:
            self.users_db = {}

    def save_users_database(self):
        """Save users database to file"""
        try:
            with open(self.users_db_file, 'w') as f:
                json.dump(self.users_db, f, indent=2, default=str)
        except Exception as e:
            raise PKIError(f"Failed to save users database: {e}")

    def register_user(self, username, email=None, organization=None):
        """
        Register a new user with enhanced identity verification

        Args:
            username: Unique username
            email: User's email address (optional)
            organization: User's organization (optional)

        Returns:
            dict: Registration result with user details
        """
        if not username or len(username) < 2:
            raise ValidationError("Username must be at least 2 characters long")

        if username in self.users_db:
            raise ValidationError(f"User '{username}' already exists")

        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Generate unique user ID
        user_id = secrets.token_hex(16)

        # Save private key with password protection option
        key_filename = f"{username}_key.pem"
        with open(key_filename, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.TraditionalOpenSSL,
                encryption_algorithm=serialization.NoEncryption()  # Can be enhanced with password
            ))

        # Create certificate signing request (CSR)
        subject_components = [
            x509.NameAttribute(NameOID.COUNTRY_NAME, "NP"),
            x509.NameAttribute(NameOID.COMMON_NAME, username),
            x509.NameAttribute(NameOID.USER_ID, user_id)
        ]

        if email:
            subject_components.append(x509.NameAttribute(NameOID.EMAIL_ADDRESS, email))
        if organization:
            subject_components.append(x509.NameAttribute(NameOID.ORGANIZATION_NAME, organization))

        subject = x509.Name(subject_components)
        csr = x509.CertificateSigningRequestBuilder().subject_name(subject).sign(private_key, hashes.SHA256())

        # Load CA private key and certificate
        try:
            with open("ca_key.pem", "rb") as f:
                ca_private_key = serialization.load_pem_private_key(f.read(), password=None)

            with open("ca_cert.pem", "rb") as f:
                ca_cert = x509.load_pem_x509_certificate(f.read())
        except FileNotFoundError:
            raise CertificateError("CA not initialized. Please initialize CA first.")

        # Create user certificate with enhanced attributes
        cert = x509.CertificateBuilder().subject_name(csr.subject).issuer_name(
            ca_cert.subject
        ).public_key(csr.public_key()).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.now(timezone.utc)
        ).not_valid_after(
            datetime.now(timezone.utc) + timedelta(days=365)
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_agreement=False,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=True,
                data_encipherment=True,
                encipher_only=False,
                decipher_only=False
            ),
            critical=True
        ).add_extension(
            x509.ExtendedKeyUsage([
                x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH,
                x509.oid.ExtendedKeyUsageOID.EMAIL_PROTECTION
            ]),
            critical=True
        ).sign(ca_private_key, hashes.SHA256())

        # Save certificate
        cert_filename = f"{username}_cert.pem"
        with open(cert_filename, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))

        # Store user information in database
        user_info = {
            'user_id': user_id,
            'username': username,
            'email': email,
            'organization': organization,
            'registration_date': datetime.now(timezone.utc).isoformat(),
            'certificate_file': cert_filename,
            'private_key_file': key_filename,
            'certificate_serial': str(cert.serial_number),
            'status': 'active'
        }

        self.users_db[username] = user_info
        self.save_users_database()

        print(f"[✓] User '{username}' registered successfully with ID: {user_id}")
        return user_info

    def authenticate_user(self, username, challenge_data=None):
        """
        Authenticate user by verifying certificate and private key possession

        Args:
            username: Username to authenticate
            challenge_data: Optional challenge data for proof of possession

        Returns:
            dict: Authentication result
        """
        if username not in self.users_db:
            raise ValidationError(f"User '{username}' not found")

        user_info = self.users_db[username]

        if user_info['status'] != 'active':
            raise ValidationError(f"User '{username}' is not active")

        # Verify certificate exists and is valid
        cert_file = user_info['certificate_file']
        key_file = user_info['private_key_file']

        if not os.path.exists(cert_file) or not os.path.exists(key_file):
            raise CertificateError(f"Certificate or key file missing for user '{username}'")

        # Load and verify certificate
        with open(cert_file, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())

        # Verify certificate is still valid
        now = datetime.now(timezone.utc)
        # Use the new UTC-aware properties if available, fallback to naive
        try:
            not_valid_before = cert.not_valid_before_utc
            not_valid_after = cert.not_valid_after_utc
        except AttributeError:
            # Fallback for older cryptography versions
            not_valid_before = cert.not_valid_before.replace(tzinfo=timezone.utc)
            not_valid_after = cert.not_valid_after.replace(tzinfo=timezone.utc)

        if now < not_valid_before or now > not_valid_after:
            raise CertificateError(f"Certificate for user '{username}' has expired")

        # Verify certificate against CA
        from verifier import verify_certificate
        if not verify_certificate(cert):
            raise CertificateError(f"Certificate validation failed for user '{username}'")

        # Proof of private key possession
        if challenge_data:
            try:
                with open(key_file, "rb") as f:
                    private_key = serialization.load_pem_private_key(f.read(), password=None)

                # Sign challenge data to prove possession of private key
                signature = private_key.sign(
                    challenge_data.encode() if isinstance(challenge_data, str) else challenge_data,
                    padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                    hashes.SHA256()
                )

                # Verify signature with public key from certificate
                public_key = cert.public_key()
                public_key.verify(
                    signature,
                    challenge_data.encode() if isinstance(challenge_data, str) else challenge_data,
                    padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                    hashes.SHA256()
                )

                print(f"[✓] User '{username}' authenticated successfully with private key proof")

            except Exception as e:
                raise ValidationError(f"Private key possession proof failed: {e}")
        else:
            print(f"[✓] User '{username}' certificate validated successfully")

        return {
            'username': username,
            'user_id': user_info['user_id'],
            'authenticated': True,
            'certificate_serial': user_info['certificate_serial'],
            'authentication_time': datetime.now(timezone.utc).isoformat()
        }

    def get_user_info(self, username):
        """Get user information"""
        if username not in self.users_db:
            raise ValidationError(f"User '{username}' not found")
        return self.users_db[username]

    def list_users(self):
        """List all registered users"""
        return list(self.users_db.keys())

    def revoke_user(self, username, reason="User requested"):
        """Revoke user certificate"""
        if username not in self.users_db:
            raise ValidationError(f"User '{username}' not found")

        self.users_db[username]['status'] = 'revoked'
        self.users_db[username]['revocation_date'] = datetime.now(timezone.utc).isoformat()
        self.users_db[username]['revocation_reason'] = reason
        self.save_users_database()

        print(f"[✓] User '{username}' certificate revoked: {reason}")


# Global authentication system instance
auth_system = UserAuthenticationSystem()

# Backward compatibility functions
def register_user(username, email=None, organization=None):
    """Register a new user (backward compatible)"""
    return auth_system.register_user(username, email, organization)

def authenticate_user(username, challenge_data=None):
    """Authenticate a user"""
    return auth_system.authenticate_user(username, challenge_data)

def get_user_info(username):
    """Get user information"""
    return auth_system.get_user_info(username)

def list_users():
    """List all users"""
    return auth_system.list_users()

def revoke_user(username, reason="User requested"):
    """Revoke user certificate"""
    return auth_system.revoke_user(username, reason)
