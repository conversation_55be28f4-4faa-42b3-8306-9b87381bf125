"""
Utility Functions for PKI Document Signing System
Provides helper functions for certificate validation, key management, file operations, and logging
"""

import os
import logging
import shutil
from datetime import datetime
from pathlib import Path
from cryptography import x509
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa


# Configure logging
def setup_logging(log_level=logging.INFO, log_file="pki_system.log"):
    """
    Setup logging configuration for the PKI system

    Args:
        log_level: Logging level (default: INFO)
        log_file: Log file name (default: pki_system.log)
    """
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


logger = setup_logging()


def validate_file_exists(file_path):
    """
    Validate that a file exists

    Args:
        file_path: Path to the file

    Returns:
        bool: True if file exists, False otherwise
    """
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return False
    return True


def create_backup(file_path, backup_suffix=".backup"):
    """
    Create a backup of a file

    Args:
        file_path: Path to the file to backup
        backup_suffix: Suffix for backup file (default: .backup)

    Returns:
        str: Path to backup file if successful, None otherwise
    """
    try:
        if not validate_file_exists(file_path):
            return None

        backup_path = f"{file_path}{backup_suffix}"
        shutil.copy2(file_path, backup_path)
        logger.info(f"Backup created: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Failed to create backup: {e}")
        return None


def restore_from_backup(original_path, backup_path):
    """
    Restore a file from backup

    Args:
        original_path: Path to restore to
        backup_path: Path of backup file

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if not validate_file_exists(backup_path):
            return False

        shutil.copy2(backup_path, original_path)
        logger.info(f"File restored from backup: {original_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to restore from backup: {e}")
        return False


def clean_temp_files(pattern="*.tmp"):
    """
    Clean temporary files matching a pattern

    Args:
        pattern: File pattern to match (default: *.tmp)
    """
    try:
        current_dir = Path(".")
        temp_files = list(current_dir.glob(pattern))

        for temp_file in temp_files:
            temp_file.unlink()
            logger.info(f"Removed temporary file: {temp_file}")

        logger.info(f"Cleaned {len(temp_files)} temporary files")
    except Exception as e:
        logger.error(f"Failed to clean temporary files: {e}")


def get_certificate_info(cert_path):
    """
    Extract information from a certificate

    Args:
        cert_path: Path to certificate file

    Returns:
        dict: Certificate information or None if failed
    """
    try:
        if not validate_file_exists(cert_path):
            return None

        with open(cert_path, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())

        # Extract certificate information
        subject = cert.subject
        issuer = cert.issuer

        info = {
            'subject': {attr.oid._name: attr.value for attr in subject},
            'issuer': {attr.oid._name: attr.value for attr in issuer},
            'serial_number': str(cert.serial_number),
            'not_valid_before': cert.not_valid_before,
            'not_valid_after': cert.not_valid_after,
            'signature_algorithm': cert.signature_algorithm_oid._name,
            'public_key_size': cert.public_key().key_size
        }

        return info
    except Exception as e:
        logger.error(f"Failed to extract certificate info: {e}")
        return None


def get_key_info(key_path):
    """
    Extract information from a private key

    Args:
        key_path: Path to private key file

    Returns:
        dict: Key information or None if failed
    """
    try:
        if not validate_file_exists(key_path):
            return None

        with open(key_path, "rb") as f:
            private_key = serialization.load_pem_private_key(f.read(), password=None)

        info = {
            'key_type': type(private_key).__name__,
            'key_size': private_key.key_size,
            'public_numbers': private_key.public_key().public_numbers().n if isinstance(private_key, rsa.RSAPrivateKey) else None
        }

        return info
    except Exception as e:
        logger.error(f"Failed to extract key info: {e}")
        return None


def list_pki_files(directory="."):
    """
    List all PKI-related files in a directory

    Args:
        directory: Directory to search (default: current directory)

    Returns:
        dict: Dictionary of file types and their paths
    """
    try:
        dir_path = Path(directory)
        pki_files = {
            'certificates': list(dir_path.glob("*_cert.pem")) + list(dir_path.glob("ca_cert.pem")),
            'private_keys': list(dir_path.glob("*_key.pem")) + list(dir_path.glob("ca_key.pem")),
            'signatures': list(dir_path.glob("*.sig")),
            'encrypted_files': list(dir_path.glob("*.enc")),
            'documents': [f for f in dir_path.glob("*.txt") if not f.name.endswith("_decrypted.txt")]
        }

        logger.info(f"Found PKI files in {directory}:")
        for file_type, files in pki_files.items():
            logger.info(f"  {file_type}: {len(files)} files")

        return pki_files
    except Exception as e:
        logger.error(f"Failed to list PKI files: {e}")
        return {}


def verify_pki_setup():
    """
    Verify that the PKI system is properly set up

    Returns:
        dict: Status of PKI components
    """
    status = {
        'ca_cert': validate_file_exists("ca_cert.pem"),
        'ca_key': validate_file_exists("ca_key.pem"),
        'users': []
    }

    # Check for user certificates and keys
    current_dir = Path(".")
    cert_files = list(current_dir.glob("*_cert.pem"))

    for cert_file in cert_files:
        if cert_file.name != "ca_cert.pem":
            username = cert_file.name.replace("_cert.pem", "")
            key_file = f"{username}_key.pem"

            user_status = {
                'username': username,
                'certificate': True,
                'private_key': validate_file_exists(key_file)
            }
            status['users'].append(user_status)

    logger.info("PKI Setup Status:")
    logger.info(f"  CA Certificate: {'OK' if status['ca_cert'] else 'MISSING'}")
    logger.info(f"  CA Private Key: {'OK' if status['ca_key'] else 'MISSING'}")
    logger.info(f"  Registered Users: {len(status['users'])}")

    return status


def generate_system_report():
    """
    Generate a comprehensive system report

    Returns:
        dict: System report with all relevant information
    """
    report = {
        'timestamp': str(datetime.now()),
        'pki_setup': verify_pki_setup(),
        'files': list_pki_files(),
        'certificates': {},
        'keys': {}
    }

    # Add certificate information
    for cert_file in report['files']['certificates']:
        cert_info = get_certificate_info(str(cert_file))
        if cert_info:
            report['certificates'][cert_file.name] = cert_info

    # Add key information
    for key_file in report['files']['private_keys']:
        key_info = get_key_info(str(key_file))
        if key_info:
            report['keys'][key_file.name] = key_info

    return report


def save_report_to_file(report, filename="pki_system_report.txt"):
    """
    Save system report to a file

    Args:
        report: Report dictionary from generate_system_report()
        filename: Output filename (default: pki_system_report.txt)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with open(filename, "w") as f:
            f.write("PKI DOCUMENT SIGNING SYSTEM REPORT\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Generated: {report['timestamp']}\n\n")

            # PKI Setup Status
            f.write("PKI SETUP STATUS:\n")
            f.write("-" * 20 + "\n")
            setup = report['pki_setup']
            f.write(f"CA Certificate: {'OK' if setup['ca_cert'] else 'MISSING'}\n")
            f.write(f"CA Private Key: {'OK' if setup['ca_key'] else 'MISSING'}\n")
            f.write(f"Registered Users: {len(setup['users'])}\n")

            for user in setup['users']:
                f.write(f"  - {user['username']}: Cert {'OK' if user['certificate'] else 'MISSING'}, Key {'OK' if user['private_key'] else 'MISSING'}\n")

            f.write("\n")

            # File Summary
            f.write("FILE SUMMARY:\n")
            f.write("-" * 15 + "\n")
            files = report['files']
            for file_type, file_list in files.items():
                f.write(f"{file_type.title()}: {len(file_list)}\n")
                for file_path in file_list:
                    f.write(f"  - {file_path.name}\n")

            f.write("\n")

            # Certificate Details
            if report['certificates']:
                f.write("CERTIFICATE DETAILS:\n")
                f.write("-" * 20 + "\n")
                for cert_name, cert_info in report['certificates'].items():
                    f.write(f"{cert_name}:\n")
                    f.write(f"  Subject: {cert_info['subject']}\n")
                    f.write(f"  Issuer: {cert_info['issuer']}\n")
                    f.write(f"  Valid From: {cert_info['not_valid_before']}\n")
                    f.write(f"  Valid Until: {cert_info['not_valid_after']}\n")
                    f.write(f"  Key Size: {cert_info['public_key_size']} bits\n")
                    f.write(f"  Serial: {cert_info['serial_number']}\n\n")

        logger.info(f"System report saved to: {filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to save report: {e}")
        return False