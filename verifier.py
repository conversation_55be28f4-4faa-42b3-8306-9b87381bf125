from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography import x509

def verify_signature(username, doc_path):
    with open(f"{username}_cert.pem", "rb") as f:
        cert = x509.load_pem_x509_certificate(f.read())
        public_key = cert.public_key()

    with open(doc_path, "rb") as f:
        data = f.read()
    with open(f"{doc_path}.sig", "rb") as f:
        signature = f.read()

    try:
        public_key.verify(
            signature, data,
            padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
            hashes.SHA256()
        )
        print(f"[✓] Signature is valid for '{doc_path}' by {username}.")
    except Exception as e:
        print(f"[✗] Signature verification failed: {e}")
