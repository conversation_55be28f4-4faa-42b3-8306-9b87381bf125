from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes, serialization
from cryptography import x509
from datetime import datetime, timezone
from error_handler import (
    CertificateError, SignatureError, ValidationError,
    handle_pki_errors, validate_file_path, validate_username,
    PKIErrorContext
)


@handle_pki_errors(CertificateError)
def verify_certificate(cert):
    """
    Verify certificate validity and CA signature

    Args:
        cert: X.509 certificate to verify

    Returns:
        bool: True if certificate is valid, False otherwise

    Raises:
        CertificateError: If certificate validation fails
    """
    with PKIErrorContext("Certificate Verification", CertificateError):
        # Validate CA certificate exists
        validate_file_path("ca_cert.pem")

        # Check if certificate is within validity period
        now = datetime.now(timezone.utc).replace(tzinfo=None)  # Make naive for comparison
        if now < cert.not_valid_before or now > cert.not_valid_after:
            raise CertificateError(
                "Certificate is expired or not yet valid",
                error_code="CERT_EXPIRED",
                details={
                    'not_valid_before': cert.not_valid_before,
                    'not_valid_after': cert.not_valid_after,
                    'current_time': now
                }
            )

        # Load CA certificate
        with open("ca_cert.pem", "rb") as f:
            ca_cert = x509.load_pem_x509_certificate(f.read())
            ca_public_key = ca_cert.public_key()

        # Verify certificate signature with CA public key
        try:
            ca_public_key.verify(
                cert.signature,
                cert.tbs_certificate_bytes,
                padding.PKCS1v15(),
                cert.signature_hash_algorithm
            )
        except Exception as e:
            raise CertificateError(
                "Certificate signature verification failed",
                error_code="CERT_SIG_INVALID",
                details={'verification_error': str(e)}
            )

        return True


@handle_pki_errors(SignatureError)
def verify_signature(username, doc_path):
    """
    Verify document signature with certificate validation

    Args:
        username: Username of the signer
        doc_path: Path to the document to verify

    Returns:
        bool: True if signature is valid

    Raises:
        SignatureError: If signature verification fails
        ValidationError: If input validation fails
    """
    with PKIErrorContext("Signature Verification", SignatureError):
        # Validate inputs
        validate_username(username)
        validate_file_path(doc_path)
        validate_file_path(f"{doc_path}.sig")

        cert_path = f"{username}_cert.pem"
        validate_file_path(cert_path)

        # Load user certificate
        with open(cert_path, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())

        # Verify certificate first
        try:
            verify_certificate(cert)
        except CertificateError as e:
            raise SignatureError(
                f"Certificate validation failed for user {username}",
                error_code="CERT_INVALID",
                details={'certificate_error': str(e)}
            )

        public_key = cert.public_key()

        # Load document and signature
        with open(doc_path, "rb") as f:
            data = f.read()
        with open(f"{doc_path}.sig", "rb") as f:
            signature = f.read()

        # Verify signature
        try:
            public_key.verify(
                signature, data,
                padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH),
                hashes.SHA256()
            )
        except Exception as e:
            raise SignatureError(
                f"Signature verification failed for document '{doc_path}' by {username}",
                error_code="SIG_INVALID",
                details={
                    'username': username,
                    'document': doc_path,
                    'verification_error': str(e)
                }
            )

        print(f"[✓] Signature is valid for '{doc_path}' by {username}.")
        return True
